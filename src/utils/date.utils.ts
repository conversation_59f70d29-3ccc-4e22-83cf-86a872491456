import { DateTime } from 'luxon';

/**
 * Returns the current date/time in the specified IANA timezone as a Date object.
 * @param timeZone - IANA timezone string (e.g., 'Asia/Dubai', 'America/New_York')
 */
export function getNowInTimezone(timeZone: string): Date {
  return DateTime.now().setZone(timeZone).toJSDate();
}

export const DUBAI_TZ = 'Asia/Dubai';

export function getMondayOfCurrentWeek(
  today: Date = getNowInTimezone(DUBAI_TZ),
): Date {
  const day = today.getDay();
  const diff = today.getDate() - day + (day === 0 ? -6 : 1);
  const monday = new Date(today);
  monday.setDate(diff);
  monday.setHours(0, 0, 0, 0);
  return monday;
}

export function formatDate(date: Date, timeZone: string = DUBAI_TZ): string {
  // Always format as YYYY-MM-DD in the given timezone
  const tzString = date.toLocaleString('en-US', { timeZone });
  const tzDate = new Date(tzString);
  return tzDate.toISOString().slice(0, 10);
}

export function clampToToday(date: Date, timeZone: string = DUBAI_TZ): Date {
  const today = getNowInTimezone(timeZone);
  today.setHours(0, 0, 0, 0);
  return date > today ? today : date;
}

export function isFutureDate(date: Date, timeZone: string = DUBAI_TZ): boolean {
  const today = getNowInTimezone(timeZone);
  today.setHours(0, 0, 0, 0);
  return date > today;
}

export function isWeekend(
  date: Date,
  workingDays: string[],
  timeZone: string = DUBAI_TZ,
): boolean {
  const dayOfWeek = date.toLocaleDateString('en-US', {
    weekday: 'short',
    timeZone,
  });
  return !workingDays.includes(dayOfWeek);
}

export function isHoliday(
  date: Date,
  holidays: string[],
  timeZone: string = DUBAI_TZ,
): boolean {
  const tzString = date.toLocaleString('en-US', { timeZone });
  const tzDate = new Date(tzString);
  return holidays.includes(tzDate.toDateString());
}

export function diffMinutes(start: Date, end: Date): number {
  return Math.floor((end.getTime() - start.getTime()) / (1000 * 60));
}

export function formatDuration(minutes: number): string {
  const h = Math.floor(minutes / 60);
  const m = minutes % 60;
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
}

// --- Time Attendance Reusable Helpers ---

export function getStartOfDay(date: Date, timeZone: string): Date {
  return DateTime.fromJSDate(date, { zone: timeZone })
    .startOf('day')
    .toJSDate();
}

export function getEndOfDay(date: Date, timeZone: string): Date {
  return DateTime.fromJSDate(date, { zone: timeZone }).endOf('day').toJSDate();
}

export function localToUTC(
  year: number,
  month: number,
  day: number,
  hour: number,
  minute: number,
  timeZone: string,
): Date {
  return DateTime.fromObject(
    { year, month, day, hour, minute },
    { zone: timeZone },
  )
    .toUTC()
    .toJSDate();
}

export function utcToLocal(date: Date, timeZone: string): Date {
  return DateTime.fromJSDate(date).setZone(timeZone).toJSDate();
}

export function getLocalDateString(date: Date, timeZone: string): string {
  return DateTime.fromJSDate(date).setZone(timeZone).toFormat('yyyy-MM-dd');
}

export function getWorkedMinutes(
  clockIn: Date,
  clockOut: Date,
  timeZone: string,
): number {
  const inLocal = DateTime.fromJSDate(clockIn).setZone(timeZone);
  const outLocal = DateTime.fromJSDate(clockOut).setZone(timeZone);
  return Math.max(0, Math.floor(outLocal.diff(inLocal, 'minutes').minutes));
}

export function isWeekendLocal(
  date: Date,
  workingDays: string[],
  timeZone: string,
): boolean {
  const dayOfWeek = DateTime.fromJSDate(date).setZone(timeZone).toFormat('ccc'); // 'Mon', 'Tue', etc.
  return !workingDays.includes(dayOfWeek);
}

export function isHolidayLocal(
  date: Date,
  holidays: string[],
  timeZone: string,
): boolean {
  const localDateStr = getLocalDateString(date, timeZone);
  return holidays.includes(localDateStr);
}

import { getWorkStartTime } from 'src/constants/attendance.constants';

export function getDateRange(month: number, year: number) {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0, 23, 59, 59, 999);
  return { startDate, endDate };
}

export function getDaysInMonth(month: number, year: number) {
  const date = new Date(year, month, 0);
  return date.getDate();
}

export function isLateClockIn(
  clockInTime: Date | null,
  timeZone: string,
): boolean {
  if (!clockInTime) {
    return false;
  }

  // Convert clockInTime to user's timezone for comparison
  const localClockIn = new Date(
    clockInTime.toLocaleString('en-US', { timeZone }),
  );

  // Get work start time in user's timezone
  const workStartTime = getWorkStartTime(clockInTime, timeZone);

  return localClockIn.getTime() > workStartTime.getTime();
}

export function isToday(date: Date): boolean {
  const today = new Date();
  return (
    today.getDate() === date.getDate() &&
    today.getMonth() === date.getMonth() &&
    today.getFullYear() === date.getFullYear()
  );
}

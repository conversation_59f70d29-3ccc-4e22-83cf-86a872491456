import { <PERSON>, <PERSON>, <PERSON><PERSON>, HttpStatus, Render } from '@nestjs/common';
import { AppService } from './app.service';
import { PublicRoute } from './modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { Response } from 'express';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @Render('index') // corresponds to views/index.hbs
  getHome() {
    return {
      title: 'Welcome to NestJS with Handlebars',
      message: 'This page is rendered using Handlebars!',
    };
  }

  @Get()
  @PublicRoute()
  redirectToApi(@Res() res: Response): void {
    res.status(HttpStatus.MOVED_PERMANENTLY).redirect('/api');
  }

  @Get('home')
  @PublicRoute()
  getHomePage(@Res() res: Response): void {
    return this.appService.getHomePage(res);
  }

  @Get('health')
  @PublicRoute()
  async getHealth() {
    return this.appService.getHealthCheck();
  }
}

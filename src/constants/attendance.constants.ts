export const DEFAULT_WORKING_DAYS = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];

// Labels
export const PRESENT_LABEL = 'present';
export const ABSENT_LABEL = 'absent';
export const WEEKEND_LABEL = 'weekend';
export const HOLIDAY_LABEL = 'holiday';
export const OVERTIME_LABEL = 'overtime';

// Work hours configuration
export const WORK_START_HOUR = 9; // 9 AM
export const WORK_START_MINUTE = 0;
export const STANDARD_WORK_HOURS = 9; // 9 hours per day

// Time format for display
export const TIME_FORMAT = {
  hour: '2-digit',
  minute: '2-digit',
  hour12: true,
} as const;

// Convert work start time to user's timezone
export function getWorkStartTime(date: Date, timeZone: string): Date {
  const localDate = new Date(date.toLocaleString('en-US', { timeZone }));
  localDate.setHours(WORK_START_HOUR, WORK_START_MINUTE, 0, 0);
  return localDate;
}

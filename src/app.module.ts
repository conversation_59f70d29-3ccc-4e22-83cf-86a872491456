import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import configuration from './config/configuration';
import cronJobsConfig from './modules/cron-jobs/cron-jobs.config';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './modules/auth/auth.module';

import { DepartmentsModule } from './modules/departments/departments.module';
import { LeaveRequestsModule } from './modules/leave-requests/leave-requests.module';
import { TimeAttendanceModule } from './modules/time-attendance/time-attendance.module';
import { PayrollsModule } from './modules/payrolls/payrolls.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { PerformanceReviewsModule } from './modules/performance-reviews/performance-reviews.module';
import { ProjectBonusesModule } from './modules/project-bonuses/project-bonuses.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { CompanySettingsModule } from './modules/company-settings/company-settings.module';
import { ProjectsModule } from './modules/projects/projects.module';
import { CrudHelperModule } from './common/crud-helper/crud-helper.module';
import { APP_INTERCEPTOR, APP_GUARD } from '@nestjs/core';
import { ResponseInterceptor } from './common/dto/interceptors/response.interceptor';
import { RolesModule } from './modules/roles-and-permissions/roles/roles.module';
import { PermissionsModule } from './modules/roles-and-permissions/permissions/permissions.module';
import { UserRoleModule } from './modules/roles-and-permissions/user-role/user-role.module';
import { PlatformSectionsModule } from './modules/roles-and-permissions/platform-sections/platform-sections.module';
import { CompanyModule } from './modules/company/company.module';
import { PermissionGuard } from './modules/auth/guards/permission.guard';
import { RedisCacheService } from './common/services/redis-cache.service';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ThrottlingModule } from './modules/throttling';
import { CompensationsModule } from './modules/compensations/compensations.module';
import { FilesModule } from './modules/files/files.module';
import { BankInfoModule } from './modules/bank-info/bank-info.module';
import { EmailModule } from './modules/email/email.module';
import { CronJobsModule } from './modules/cron-jobs/cron-jobs.module';
import { CountriesModule } from './modules/countries';
import { PositionsModule } from './modules/positions/positions.module';
import { AssetsModule } from './modules/assets/assets.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
      load: [configuration, cronJobsConfig],
    }),
    ScheduleModule.forRoot(),
    ThrottlingModule,
    RolesModule,
    PermissionsModule,
    UserRoleModule,
    PlatformSectionsModule,
    PermissionsModule,
    DatabaseModule,
    UsersModule,
    AuthModule,

    DepartmentsModule,
    LeaveRequestsModule,
    TimeAttendanceModule,
    PayrollsModule,
    DocumentsModule,
    PerformanceReviewsModule,
    ProjectBonusesModule,
    NotificationsModule,
    DashboardModule,
    CompanySettingsModule,
    ProjectsModule,
    CrudHelperModule,
    UserRoleModule,
    CompanyModule,
    CompensationsModule,
    FilesModule,
    BankInfoModule,
    EmailModule,
    CronJobsModule,
    CountriesModule,
    PositionsModule,
    AssetsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    RedisCacheService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: PermissionGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}

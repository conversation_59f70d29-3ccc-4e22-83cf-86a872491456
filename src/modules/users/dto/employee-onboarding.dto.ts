import {
  IsEmail,
  Is<PERSON>num,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsDateString,
  IsArray,
  ValidateNested,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { GENDER_TYPES_ENUM, MARITAL_STATUS_ENUM } from 'src/utils/enums';
import {
  ContractTypeEnum,
  EmploymentTypeEnum,
  WorkingHoursEnum,
  WorkingDaysEnum,
} from '../enums/employment.enums';

// Personal Details DTO
export class CreateEmployeePersonalDetailsDto {
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  username?: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsNumber()
  roleId?: number;

  @IsOptional()
  @IsNumber()
  nationality?: number;

  @IsOptional()
  @IsDateString()
  dob?: Date;

  @IsOptional()
  @IsEnum(GENDER_TYPES_ENUM)
  gender?: GENDER_TYPES_ENUM;

  @IsOptional()
  @IsString()
  passportNumber?: string;

  @IsOptional()
  @IsEnum(MARITAL_STATUS_ENUM)
  maritalStatus?: MARITAL_STATUS_ENUM;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  whatsappNumber?: string;

  @IsOptional()
  @IsString()
  emergencyContact?: string;

  @IsOptional()
  @IsString()
  emergencyContactRelation?: string;

  @IsOptional()
  @IsString()
  emergencyContactName?: string;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  pinCode?: string;
}

// Employment Information DTO
export class CreateEmployeeEmploymentInfoDto {
  @IsOptional()
  @IsString()
  employeeId?: string;

  @IsOptional()
  @IsNumber()
  departmentId?: number;

  @IsOptional()
  @IsString()
  position?: string;

  @IsOptional()
  @IsString()
  designation?: string;

  @IsOptional()
  @IsDateString()
  hireDate?: Date;

  @IsOptional()
  @IsEnum(ContractTypeEnum)
  typeOfContract?: ContractTypeEnum;

  @IsOptional()
  @IsString()
  contractDuration?: string;

  @IsOptional()
  @IsEnum(EmploymentTypeEnum)
  typeOfEmployment?: EmploymentTypeEnum;

  @IsOptional()
  @IsEnum(WorkingHoursEnum)
  workingHours?: WorkingHoursEnum;

  @IsOptional()
  @IsEnum(WorkingDaysEnum)
  workingDays?: WorkingDaysEnum;

  @IsOptional()
  @IsNumber()
  probationPeriod?: number;

  @IsOptional()
  @IsDateString()
  probationEndDate?: Date;

  @IsOptional()
  @IsNumber()
  reportingManagerId?: number;

  @IsOptional()
  @IsString()
  companyEmail?: string;

  @IsOptional()
  @IsString()
  companyPhoneNumber?: string;
}

// Benefit DTO for nested use in compensation
class CreateBenefitNestedDto {
  @IsString()
  allowance: string;

  @IsString()
  frequency: string;

  @IsNumber()
  @Type(() => Number)
  amount: number;
}

// Compensation DTO
export class CreateEmployeeCompensationDto {
  @IsDateString()
  effectiveFrom: Date;

  @IsDateString()
  @IsOptional()
  reviewDate?: Date;

  @IsString()
  currency: string;

  @IsString()
  frequency: string;

  @IsNumber()
  totalSalary: number;

  @IsNumber()
  basicSalary: number;

  @IsNumber()
  @IsOptional()
  housing?: number;

  @IsNumber()
  @IsOptional()
  transportation?: number;

  @IsNumber()
  @IsOptional()
  others?: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateBenefitNestedDto)
  @IsOptional()
  benefits?: CreateBenefitNestedDto[];
}

// Banking Details DTO
export class CreateEmployeeBankInfoDto {
  @IsString()
  holderName: string;

  @IsString()
  bankName: string;

  @IsString()
  branchName: string;

  @IsString()
  accountNumber: string;

  @IsString()
  @IsOptional()
  iban?: string;

  @IsString()
  @IsOptional()
  swiftCode?: string;

  @IsString()
  @IsOptional()
  bankAddress?: string;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  routingNumber?: string;

  @IsString()
  @IsOptional()
  branchCode?: string;

  @IsBoolean()
  @IsOptional()
  isPrimary?: boolean;
}

// Update DTOs
export class UpdateEmployeePersonalDetailsDto extends CreateEmployeePersonalDetailsDto {}
export class UpdateEmployeeEmploymentInfoDto extends CreateEmployeeEmploymentInfoDto {}
export class UpdateEmployeeCompensationDto extends CreateEmployeeCompensationDto {}
export class UpdateEmployeeBankInfoDto extends CreateEmployeeBankInfoDto {}

// General Update Employee DTO (for backward compatibility)
export class UpdateEmployeeDto {
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => UpdateEmployeePersonalDetailsDto)
  personalInfo?: UpdateEmployeePersonalDetailsDto;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => UpdateEmployeeEmploymentInfoDto)
  employmentInfo?: UpdateEmployeeEmploymentInfoDto;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => UpdateEmployeeCompensationDto)
  compensation?: UpdateEmployeeCompensationDto;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => UpdateEmployeeBankInfoDto)
  bankingDetails?: UpdateEmployeeBankInfoDto;
}

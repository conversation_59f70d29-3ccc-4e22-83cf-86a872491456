import {
  IsE<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsDateString,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { GENDER_TYPES_ENUM, MARITAL_STATUS_ENUM } from 'src/utils/enums';
import {
  ContractTypeEnum,
  EmploymentTypeEnum,
  WorkingHoursEnum,
  WorkingDaysEnum,
} from '../enums/employment.enums';

// Personal Details DTO
export class CreateEmployeePersonalDetailsDto {
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  middleName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsOptional()
  @IsDateString()
  dob?: Date;

  @IsOptional()
  @IsEnum(GENDER_TYPES_ENUM)
  gender?: GENDER_TYPES_ENUM;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsEnum(MARITAL_STATUS_ENUM)
  maritalStatus?: MARITAL_STATUS_ENUM;

  @IsOptional()
  @IsNumber()
  nationality?: number;

  @IsOptional()
  @IsString()
  passportNumber?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  whatsappNumber?: string;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsString()
  emergencyName?: string;

  @IsOptional()
  @IsString()
  emergencyRelation?: string;

  @IsOptional()
  @IsString()
  emergencyPhone?: string;

  @IsOptional()
  @IsNumber()
  countryId?: number;

  @IsOptional()
  @IsString()
  zipCode?: string;
}

// Employment Information DTO
export class CreateEmployeeEmploymentInfoDto {
  @IsOptional()
  @IsString()
  employeeId?: string;

  @IsOptional()
  @IsDateString()
  joiningDate?: Date;

  @IsOptional()
  @IsEnum(ContractTypeEnum)
  typeOfContract?: ContractTypeEnum;

  @IsOptional()
  @IsString()
  contractDuration?: string;

  @IsOptional()
  @IsEnum(EmploymentTypeEnum)
  typeOfEmployment?: EmploymentTypeEnum;

  @IsOptional()
  @IsEnum(WorkingHoursEnum)
  workingHours?: WorkingHoursEnum;

  @IsOptional()
  @IsEnum(WorkingDaysEnum)
  workingDays?: WorkingDaysEnum;

  @IsOptional()
  @IsNumber()
  probationPeriod?: number;

  @IsOptional()
  @IsDateString()
  probationEndDate?: Date;

  @IsOptional()
  @IsNumber()
  roleId?: number;

  @IsOptional()
  @IsNumber()
  departmentId?: number;

  @IsOptional()
  @IsNumber()
  positionId?: number;

  @IsOptional()
  @IsDateString()
  hireDate?: Date;

  @IsOptional()
  @IsNumber()
  reportingManagerId?: number;

  @IsOptional()
  @IsNumber()
  reportingOffice?: number;
}

// Compensation DTO (NO NESTED OBJECTS)
export class CreateEmployeeCompensationDto {
  @IsDateString()
  effectiveFrom: Date;

  @IsDateString()
  @IsOptional()
  reviewDate?: Date;

  @IsString()
  currency: string;

  @IsString()
  frequency: string;

  @IsNumber()
  totalSalary: number;

  @IsNumber()
  basicSalary: number;

  @IsNumber()
  @IsOptional()
  housing?: number;

  @IsNumber()
  @IsOptional()
  transportation?: number;

  @IsNumber()
  @IsOptional()
  others?: number;
}

// Separate DTO for Benefits (NO NESTING)
export class CreateEmployeeBenefitDto {
  @IsNumber()
  compensationId: number;

  @IsString()
  allowance: string;

  @IsString()
  frequency: string;

  @IsNumber()
  @Type(() => Number)
  amount: number;
}

// Banking Details DTO
export class CreateEmployeeBankInfoDto {
  @IsString()
  holderName: string;

  @IsString()
  bankName: string;

  @IsString()
  branchName: string;

  @IsString()
  accountNumber: string;

  @IsString()
  @IsOptional()
  iban?: string;

  @IsString()
  @IsOptional()
  swiftCode?: string;

  @IsString()
  @IsOptional()
  bankAddress?: string;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  routingNumber?: string;

  @IsString()
  @IsOptional()
  branchCode?: string;

  @IsBoolean()
  @IsOptional()
  isPrimary?: boolean;
}

// Update DTOs (NO NESTED OBJECTS - Use individual endpoints instead)
export class UpdateEmployeePersonalDetailsDto extends CreateEmployeePersonalDetailsDto {}
export class UpdateEmployeeEmploymentInfoDto extends CreateEmployeeEmploymentInfoDto {}
export class UpdateEmployeeCompensationDto extends CreateEmployeeCompensationDto {}
export class UpdateEmployeeBankInfoDto extends CreateEmployeeBankInfoDto {}
export class UpdateEmployeeBenefitDto extends CreateEmployeeBenefitDto {}

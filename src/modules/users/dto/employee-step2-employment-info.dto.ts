import {
  IsEnum,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  IsString,
  IsDateString,
} from 'class-validator';
import { ContractTypeEnum } from 'src/modules/employees/enums/contract-type.enum';
import { EmploymentTypeEnum } from 'src/modules/employees/enums/employment-type.enum';
import { WorkingHoursEnum } from 'src/modules/employees/enums/working-hours.enum';
import { WorkingDaysEnum } from 'src/modules/employees/enums/working-days.enum';

export class EmployeeStep2EmploymentInfoDto {
  @IsOptional()
  @IsString()
  employeeId?: string;

  @IsOptional()
  @IsNumber()
  departmentId?: number;

  @IsOptional()
  @IsString()
  position?: string;

  @IsOptional()
  @IsString()
  designation?: string;

  @IsOptional()
  @IsDateString()
  hireDate?: Date;

  @IsOptional()
  @IsEnum(ContractTypeEnum)
  typeOfContract?: ContractTypeEnum;

  @IsOptional()
  @IsString()
  contractDuration?: string;

  @IsOptional()
  @IsEnum(EmploymentTypeEnum)
  typeOfEmployment?: EmploymentTypeEnum;

  @IsOptional()
  @IsEnum(WorkingHoursEnum)
  workingHours?: WorkingHoursEnum;

  @IsOptional()
  @IsEnum(WorkingDaysEnum)
  workingDays?: WorkingDaysEnum;

  @IsOptional()
  @IsNumber()
  probationPeriod?: number;

  @IsOptional()
  @IsDateString()
  probationEndDate?: Date;

  @IsOptional()
  @IsNumber()
  reportingManagerId?: number;

  @IsOptional()
  @IsString()
  companyEmail?: string;

  @IsOptional()
  @IsString()
  companyPhoneNumber?: string;
}

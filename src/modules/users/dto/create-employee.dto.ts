import {
  IsEmail,
  <PERSON><PERSON>num,
  IsNot<PERSON>mpty,
  <PERSON><PERSON><PERSON>ber,
  Is<PERSON>ptional,
  IsString,
  <PERSON><PERSON><PERSON>th,
  IsDateString,
  IsObject,
  ValidateNested,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { GENDER_TYPES_ENUM, MARITAL_STATUS_ENUM } from 'src/utils/enums';
import { ContractTypeEnum } from 'src/modules/employees/enums/contract-type.enum';
import { EmploymentTypeEnum } from 'src/modules/employees/enums/employment-type.enum';
import { WorkingHoursEnum } from 'src/modules/employees/enums/working-hours.enum';
import { WorkingDaysEnum } from 'src/modules/employees/enums/working-days.enum';

class PersonalInfoDto {
  @IsOptional()
  @IsNumber()
  nationality?: number;

  @IsOptional()
  @IsDateString()
  dob?: Date;

  @IsOptional()
  @IsEnum(GENDER_TYPES_ENUM)
  gender?: GENDER_TYPES_ENUM;

  @IsOptional()
  @IsString()
  passportNumber?: string;

  @IsOptional()
  @IsEnum(MARITAL_STATUS_ENUM)
  maritalStatus?: MARITAL_STATUS_ENUM;
}

class ContactInfoDto {
  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  whatsappNumber?: string;

  @IsOptional()
  @IsString()
  emergencyContact?: string;

  @IsOptional()
  @IsString()
  emergencyContactRelation?: string;

  @IsOptional()
  @IsString()
  emergencyContactName?: string;
}

class EmploymentInfoDto {
  @IsOptional()
  @IsNumber()
  departmentId?: number;

  @IsOptional()
  @IsString()
  position?: string;

  @IsOptional()
  @IsString()
  designation?: string;

  @IsOptional()
  @IsDateString()
  hireDate?: Date;

  @IsOptional()
  @IsEnum(ContractTypeEnum)
  typeOfContract?: ContractTypeEnum;

  @IsOptional()
  @IsString()
  contractDuration?: string;

  @IsOptional()
  @IsEnum(EmploymentTypeEnum)
  typeOfEmployment?: EmploymentTypeEnum;

  @IsOptional()
  @IsEnum(WorkingHoursEnum)
  workingHours?: WorkingHoursEnum;

  @IsOptional()
  @IsEnum(WorkingDaysEnum)
  workingDays?: WorkingDaysEnum;

  @IsOptional()
  @IsNumber()
  probationPeriod?: number;

  @IsOptional()
  @IsDateString()
  probationEndDate?: Date;

  @IsOptional()
  @IsNumber()
  reportingManagerId?: number;

  @IsOptional()
  @IsString()
  companyEmail?: string;

  @IsOptional()
  @IsString()
  companyPhoneNumber?: string;
}

class AddressDto {
  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  pinCode?: string;
}

export class CreateEmployeeDto {
  // Basic user info
  @IsNotEmpty()
  @IsString()
  username: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  password: string;

  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsNotEmpty()
  @IsNumber()
  roleId: number;

  // Optional nested objects
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => PersonalInfoDto)
  personalInfo?: PersonalInfoDto;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ContactInfoDto)
  contactInfo?: ContactInfoDto;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => EmploymentInfoDto)
  employmentInfo?: EmploymentInfoDto;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AddressDto)
  address?: AddressDto;
}

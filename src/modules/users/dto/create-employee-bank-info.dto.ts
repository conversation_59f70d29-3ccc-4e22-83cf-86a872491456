import { IsString, IsOptional, IsBoolean } from 'class-validator';

export class CreateEmployeeBankInfoDto {
  @IsString()
  holderName: string;

  @IsString()
  bankName: string;

  @IsString()
  branchName: string;

  @IsString()
  accountNumber: string;

  @IsString()
  @IsOptional()
  iban?: string;

  @IsString()
  @IsOptional()
  swiftCode?: string;

  @IsString()
  @IsOptional()
  bankAddress?: string;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  routingNumber?: string;

  @IsString()
  @IsOptional()
  branchCode?: string;

  @IsBoolean()
  @IsOptional()
  isPrimary?: boolean;
}

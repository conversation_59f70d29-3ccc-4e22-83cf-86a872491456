import {
  IsEmail,
  <PERSON>Enum,
  <PERSON>NotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsDateString,
  IsBoolean,
} from 'class-validator';
import { GENDER_TYPES_ENUM, MARITAL_STATUS_ENUM } from 'src/utils/enums';

export class EmployeeStep1PersonalDetailsDto {
  // Basic user info
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  middleName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsOptional()
  @IsEnum(GENDER_TYPES_ENUM)
  gender?: GENDER_TYPES_ENUM;

  @IsOptional()
  @IsDateString()
  dob?: Date;

  @IsOptional()
  @IsEnum(MARITAL_STATUS_ENUM)
  maritalStatus?: MARITAL_STATUS_ENUM;

  // Address
  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsString()
  street?: string;

  @IsOptional()
  @IsString()
  zipCode?: string;

  @IsOptional()
  @IsNumber()
  countryId?: number;

  @IsOptional()
  @IsString()
  city?: string;

  @IsNumber()
  nationality?: number;

  // Contact details

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  whatsappNumber?: string;
}

export class CreateEmployeeBankInfoDto {
  @IsString()
  holderName: string;

  @IsString()
  bankName: string;

  @IsString()
  branchName: string;

  @IsString()
  accountNumber: string;

  @IsString()
  @IsOptional()
  iban?: string;

  @IsString()
  @IsOptional()
  swiftCode?: string;

  @IsString()
  @IsOptional()
  bankAddress?: string;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  routingNumber?: string;

  @IsString()
  @IsOptional()
  branchCode?: string;

  @IsBoolean()
  @IsOptional()
  isPrimary?: boolean;
}

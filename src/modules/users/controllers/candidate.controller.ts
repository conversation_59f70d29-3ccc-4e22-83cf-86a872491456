import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UnauthorizedException,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { UsersService } from '../services/users.service';
import {
  CanCreate,
  CanDelete,
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from 'src/modules/throttling';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { CreateCandidateDto } from '../dto/create-candidate.dto';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { CreateInterviewDetailsDto } from '../dto/create-interview-details.dto';
import { SendInterviewInvitationDto } from '../dto/send-interview-invitation.dto';
import { CandidateStatus } from '../enums/invitation-status.enum';
import { SendInvitationOnlyDto } from '../dto/send-invitation-only.dto';
import { UpdateCandidateStatusDTO } from '../dto/update-invitation-status.dto';
import { SendOfferLetterDto } from '../dto/send-offer-letter.dto';
import { UpdateCandidateDto } from '../dto/update-candidate.dto';

@Controller('candidates')
@UseGuards(PermissionGuard)
export class CandidateController {
  constructor(private readonly usersService: UsersService) {}

  @Post('')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'resume', maxCount: 1 },
      { name: 'passport', maxCount: 1 },
    ]),
  )
  async createCandidate(
    @Body() createCandidateDto: CreateCandidateDto,
    @UploadedFiles()
    files: { resume?: any[]; passport?: any[] },
    @Req() req: UserRequestI,
  ) {
    // Extract JWT token
    const authHeader = req.headers['authorization'] || '';
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;
    const currentUserId = req.user.id;

    // Prepare files object for service
    const fileObjects = {
      resume: files?.resume?.[0] || null,
      passport: files?.passport?.[0] || null,
    };

    // Proceed to create candidate
    const result = await this.usersService.createCandidateWithTransaction(
      createCandidateDto,
      currentUserId,
      fileObjects,
      token,
    );

    return {
      success: true,
      message: 'Candidate created successfully',
      data: result,
    };
  }

  @Put(':id')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'resume', maxCount: 1 },
      { name: 'passport', maxCount: 1 },
    ]),
  )
  async updateCandidate(
    @Param('id', ParseIntPipe) candidateId: number,
    @Body() updateCandidateDto: UpdateCandidateDto,
    @UploadedFiles()
    files: { resume?: any[]; passport?: any[] },
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user?.id;
    if (!currentUserId) {
      throw new UnauthorizedException('User not authenticated');
    }

    const authHeader = req.headers['authorization'] || '';
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;

    // Prepare file payload
    const fileObjects = {
      resume: files?.resume?.[0] || null,
      passport: files?.passport?.[0] || null,
    };

    // Call service
    const result = await this.usersService.updateCandidateWithTransaction(
      candidateId,
      updateCandidateDto,
      currentUserId,
      fileObjects,
      token,
    );

    return {
      success: true,
      message: 'Candidate updated successfully',
      data: result,
    };
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async removeCandidate(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    return this.usersService.removeCandidate(id, currentUserId);
  }

  @Post('interview-details')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createInterviewDetails(
    @Body() createInterviewDetailsDto: CreateInterviewDetailsDto,
  ) {
    return this.usersService.createInterviewDetails(createInterviewDetailsDto);
  }

  @Post('send-and-save-interview-invitation')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async sendAndSaveInterviewInvitation(
    @Body() sendInterviewInvitationDto: SendInterviewInvitationDto,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    return this.usersService.sendInterviewInvitationAndSave(
      sendInterviewInvitationDto,
      currentUserId,
    );
  }

  @Post('send-interview-invitation')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async sendInterviewInvitation(
    @Body() sendInterviewInvitationDto: SendInvitationOnlyDto,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    return this.usersService.sendInvitationOnly(
      sendInterviewInvitationDto,
      currentUserId,
    );
  }

  @Get('summary')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getCandidateSummary(@Req() req: UserRequestI): Promise<any> {
    const userId = req.user.id;
    return this.usersService.getCandidateSummary(userId);
  }

  @Get('get')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getCandidates(
    @Req() req: UserRequestI,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('search') search?: string,
    @Query('status') status?: CandidateStatus,
    @Query('department') department?: string,
  ) {
    const { id } = req.user;
    console.log(req.user);
    const result = await this.usersService.getCandidatesWithInterviewDetails(
      id,
      Number(page),
      Number(limit),
      search,
      status,
      department,
    );

    return result;
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getCandidateById(@Param('id', ParseIntPipe) id: number) {
    return this.usersService.getCandidateById(id);
  }

  @Get('interviewers')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getInterviewers(@Req() req: UserRequestI) {
    const currentUserId = req.user.id;
    return this.usersService.getInterviewers(currentUserId);
  }

  @Post('send-invitation-only')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async sendInvitationOnly(
    @Body() sendInvitationOnlyDto: SendInvitationOnlyDto,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    const result = await this.usersService.sendInvitationOnly(
      sendInvitationOnlyDto,
      currentUserId,
    );

    return result;
  }

  @Get('interview-details/:candidateId/status')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getInterviewDetailsByStatus(
    @Req() req: UserRequestI,
    @Param('candidateId', ParseIntPipe) candidateId: number,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ) {
    const currentUserId = req.user.id;
    const result = await this.usersService.getInterviewDetailsByStatus(
      currentUserId,
      candidateId,
      Number(page),
      Number(limit),
    );

    return result;
  }

  @Put('candidate/:candidateId/status')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async updateCanidateStatus(
    @Param('candidateId', ParseIntPipe) candidateId: number,
    @Body() status: UpdateCandidateStatusDTO,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    console.log(status);
    const result = await this.usersService.updateCandidateStatus(
      candidateId,
      status.status,
      currentUserId,
    );

    return result;
  }

  @Get('positions/get')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getPositions(@Req() req: UserRequestI) {
    const currentUserId = req.user.id;
    return this.usersService.getPositions(currentUserId);
  }

  @Post('send-offer-letter')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async sendOfferLetter(
    @Body() sendOfferLetterDto: SendOfferLetterDto,
    @Req() req: UserRequestI,
  ) {
    const currentUserId = req.user.id;
    const authHeader = req.headers['authorization'] || '';
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : '';

    const result = await this.usersService.sendOfferLetter(
      sendOfferLetterDto.candidateId,
      currentUserId,
      token,
    );

    return {
      success: result.success,
      message: result.message,
    };
  }

  @Get(':id/flow-step')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getCandidateFlowStep(@Param('id', ParseIntPipe) candidateId: number) {
    const flowStep = await this.usersService.getCandidateFlowStep(candidateId);
    return {
      success: true,
      data: { candidateId, flowStep },
    };
  }

  @Put(':id/flow-step')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async updateCandidateFlowStep(
    @Param('id', ParseIntPipe) candidateId: number,
  ) {
    const flowStep =
      await this.usersService.updateCandidateFlowStep(candidateId);
    return {
      success: true,
      message: 'Candidate flow step updated successfully',
      data: { candidateId, flowStep },
    };
  }

  @Post('generate-pdf')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async generatePdfFromTemplateAndUpload(
    @Body() data: any,
    @Req() req: UserRequestI,
  ) {
    const authHeader = req.headers['authorization'] || '';
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;
    const userId = req.user.id;
    console.log(token, userId);
    return this.usersService.generatePdfFromTemplateAndUpload(
      'interview-invitation',
      token,
      userId,
      data,
    );
  }
}

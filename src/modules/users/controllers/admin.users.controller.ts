import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { GetUser } from 'src/common/decorators';
import { User } from 'src/models/users-models/user.model';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import {
  CanCreatePlatform,
  CanDeletePlatform,
  CanEditPlatform,
  CanViewPlatform,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { UsersService } from '../services/users.service';

@Controller('admin/users')
@UseGuards(PermissionGuard)
export class AdminUsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @CanViewPlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  findAllUsers() {
    return this.usersService.findAll();
  }

  @Get('paginated')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  findAllPaginated(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResult<Partial<User>>> {
    return this.usersService.getAllUsersPaginated(Number(page), Number(limit));
  }

  @Get(':id')
  @CanViewPlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Partial<User>> {
    return this.usersService.findUserBy({ query: { id } });
  }

  @Post('onboarding')
  @CanCreatePlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  async createUser(
    @Body() createUserDto: CreateUserDto,
    @GetUser('id') userId: number,
  ) {
    const createdEmployee = await this.usersService.createUser(
      createUserDto,
      userId,
    );

    return {
      message: 'User created successfully',
      data: createdEmployee,
    };
  }

  @Patch(':id')
  @CanEditPlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<Partial<User>> {
    return this.usersService.updateUser(id, updateUserDto);
  }

  @Delete(':id')
  @CanDeletePlatform(PLATFORM_SECTION_ENUM.USERS)
  @ThrottleRelaxed()
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.usersService.remove(id);
  }
}

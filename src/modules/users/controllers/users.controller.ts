import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';

import { GetUser } from 'src/common/decorators';
import { User } from '../../../models/users-models/user.model';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanCreate,
  CanDelete,
  CanEdit,
  CanView,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';
import { CreateUserDto, UpdateUserDto } from '../dto';
import { UsersService } from '../services/users.service';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';

@Controller('users')
@UseGuards(PermissionGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  findAllPaginated(
    @GetUser('companyId') companyId: number,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResult<Partial<User>>> {
    return this.usersService.getUsersByCompanyPaginated(
      companyId,
      Number(page),
      Number(limit),
    );
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Partial<User>> {
    return this.usersService.findUserBy({ query: { id } });
  }

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  create(@Body() createUserDto: CreateUserDto): Promise<Partial<User>> {
    return this.usersService.createUser(createUserDto);
  }

  @Post('upload-profile-image')
  @ThrottleRelaxed()
  async uploadProfileImage(
    @Body() file: any,
    @Req() req: UserRequestI,
  ): Promise<{ uploadResult: any }> {
    const authHeader = req.headers['authorization'] || '';
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;
    const userId = req.user.id;
    return this.usersService.uploadProfileImage(file, token, userId);
  }

  @Patch(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<Partial<User>> {
    return this.usersService.updateUser(id, updateUserDto);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.usersService.remove(id);
  }

  @Post('onboarding')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createUser(
    @Body() createUserDto: CreateUserDto,
    @GetUser('id') userId: number,
  ) {
    const createdEmployee = await this.usersService.createUser(
      createUserDto,
      userId,
    );

    return {
      message: 'User created successfully',
      data: createdEmployee,
    };
  }
}

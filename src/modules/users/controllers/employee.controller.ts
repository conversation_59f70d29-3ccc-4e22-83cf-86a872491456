import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';

import { User } from '../../../models/users-models/user.model';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';

import {
  CreateEmployeePersonalDetailsDto,
  CreateEmployeeEmploymentInfoDto,
  CreateEmployeeCompensationDto,
  CreateEmployeeBankInfoDto,
  UpdateEmployeeDto,
} from '../dto/employee-onboarding.dto';
import { UsersService } from '../services/users.service';

@Controller('employees')
@UseGuards(PermissionGuard)
export class EmployeeController {
  constructor(private readonly usersService: UsersService) {}

  @Post('onboarding/personal-details')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createEmployeePersonalDetails(
    @Body() personalDetailsDto: CreateEmployeePersonalDetailsDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeePersonalDetails(
      personalDetailsDto,
      currentUserId,
    );
  }

  @Post('onboarding/:id/employment-info')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createEmployeeEmploymentInfo(
    @Param('id', ParseIntPipe) id: number,
    @Body() employmentInfoDto: CreateEmployeeEmploymentInfoDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeEmploymentInfo(
      id,
      employmentInfoDto,
      currentUserId,
    );
  }

  @Post('onboarding/:id/compensation')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createEmployeeCompensation(
    @Param('id', ParseIntPipe) id: number,
    @Body() compensationDto: CreateEmployeeCompensationDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeCompensation(
      id,
      compensationDto,
      currentUserId,
    );
  }

  @Post('onboarding/:id/banking-details')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createEmployeeBankingDetails(
    @Param('id', ParseIntPipe) id: number,
    @Body() bankInfoDto: CreateEmployeeBankInfoDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeBankInfo(
      id,
      bankInfoDto,
      currentUserId,
    );
  }

  @Post('onboarding/:id/complete')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async completeEmployeeOnboarding(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.completeEmployeeOnboarding(id, currentUserId);
  }

  @Get('onboarding/:id/status')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getEmployeeOnboardingStatus(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.getEmployeeOnboardingStatus(id, currentUserId);
  }

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  findAllPaginated(
    @Req() req: UserRequestI,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResult<Partial<User>>> {
    const userId = req.user.id;
    return this.usersService.getEmployeesByCompanyPaginated(
      userId,
      Number(page),
      Number(limit),
    );
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getEmployeeDetails(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.getEmployeeDetails(id, currentUserId);
  }

  @Patch(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async updateEmployee(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.updateEmployee(
      id,
      updateEmployeeDto,
      currentUserId,
    );
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async deleteEmployee(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.deleteEmployee(id, currentUserId);
  }

  @Patch('bank-info/:bankInfoId')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async updateEmployeeBankInfo(
    @Param('bankInfoId', ParseIntPipe) bankInfoId: number,
    @Body() bankInfoDto: CreateEmployeeBankInfoDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.updateEmployeeBankInfo(
      bankInfoId,
      bankInfoDto,
      currentUserId,
    );
  }

  @Post(':id/generate-documents')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async generateEmployeeDocuments(
    @Param('id', ParseIntPipe) id: number,
    @Body() documentsDto: { documentTypes: string[] },
  ): Promise<any> {
    // This would integrate with document generation service
    // For now, return a placeholder response
    return {
      message: 'Document generation initiated',
      employeeId: id,
      documentTypes: documentsDto.documentTypes,
    };
  }

  @Get(':id/assets')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getEmployeeAssets(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any[]> {
    const currentUserId = req.user.id;
    return this.usersService.getEmployeeAssets(id, currentUserId);
  }

  @Post(':id/assign-asset/:assetId')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async assignAssetToEmployee(
    @Param('id', ParseIntPipe) id: number,
    @Param('assetId', ParseIntPipe) assetId: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.assignAssetToEmployee(id, assetId, currentUserId);
  }

  @Delete('unassign-asset/:assetId')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async unassignAssetFromEmployee(
    @Param('assetId', ParseIntPipe) assetId: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.unassignAssetFromEmployee(assetId, currentUserId);
  }
}

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  UseGuards,
  Req,
} from '@nestjs/common';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';

import { User } from '../../../models/users-models/user.model';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanView,
  CanCreate,
  CanEdit,
  CanDelete,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';
import { UpdateEmployeeDto } from '../dto/update-employee.dto';
import { CreateEmployeeCompensationDto } from '../dto/create-employee-compensation.dto';
import { CreateEmployeeBankInfoDto } from '../dto/create-employee-bank-info.dto';
import { EmployeeStep1PersonalDetailsDto } from '../dto/create-employee-dto';
import { EmployeeStep2EmploymentInfoDto } from '../dto/employee-step2-employment-info.dto';
import { UsersService } from '../services/users.service';

@Controller('employees')
@UseGuards(PermissionGuard)
export class EmployeeController {
  constructor(private readonly usersService: UsersService) {}

  @Post('onboarding/step1')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  // @ThrottleRelaxed()
  async createEmployeeStep1(
    @Body() personalDetailsDto: EmployeeStep1PersonalDetailsDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeStep1(
      personalDetailsDto,
      currentUserId,
    );
  }

  @Post('onboarding/:id/step2')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  // @ThrottleRelaxed()
  async createEmployeeStep2(
    @Param('id', ParseIntPipe) id: number,
    @Body() employmentInfoDto: EmployeeStep2EmploymentInfoDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeStep2(
      id,
      employmentInfoDto,
      currentUserId,
    );
  }

  @Post('onboarding/:id/step3')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createEmployeeStep3(
    @Param('id', ParseIntPipe) id: number,
    @Body() compensationDto: CreateEmployeeCompensationDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeStep3(
      id,
      compensationDto,
      currentUserId,
    );
  }

  @Post('onboarding/:id/step4')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createEmployeeStep4(
    @Param('id', ParseIntPipe) id: number,
    @Body() bankInfoDto: CreateEmployeeBankInfoDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeStep4(
      id,
      bankInfoDto,
      currentUserId,
    );
  }

  @Post('onboarding/:id/complete')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async completeEmployeeOnboarding(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.completeEmployeeOnboarding(id, currentUserId);
  }

  @Get('onboarding/:id/status')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getEmployeeOnboardingStatus(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.getEmployeeOnboardingStatus(id, currentUserId);
  }

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  findAllPaginated(
    @Req() req: UserRequestI,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResult<Partial<User>>> {
    const userId = req.user.id;
    return this.usersService.getEmployeesByCompanyPaginated(
      userId,
      Number(page),
      Number(limit),
    );
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getEmployeeDetails(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.getEmployeeDetails(id, currentUserId);
  }

  @Patch(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async updateEmployee(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.updateEmployee(
      id,
      updateEmployeeDto,
      currentUserId,
    );
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async deleteEmployee(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.deleteEmployee(id, currentUserId);
  }

  @Post(':id/compensation')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createEmployeeCompensation(
    @Param('id', ParseIntPipe) id: number,
    @Body() compensationDto: CreateEmployeeCompensationDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeCompensation(
      id,
      compensationDto,
      currentUserId,
    );
  }

  @Post(':id/bank-info')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async createEmployeeBankInfo(
    @Param('id', ParseIntPipe) id: number,
    @Body() bankInfoDto: CreateEmployeeBankInfoDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.createEmployeeBankInfo(
      id,
      bankInfoDto,
      currentUserId,
    );
  }

  @Patch('bank-info/:bankInfoId')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async updateEmployeeBankInfo(
    @Param('bankInfoId', ParseIntPipe) bankInfoId: number,
    @Body() bankInfoDto: CreateEmployeeBankInfoDto,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.updateEmployeeBankInfo(
      bankInfoId,
      bankInfoDto,
      currentUserId,
    );
  }

  @Post(':id/generate-documents')
  @CanCreate(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async generateEmployeeDocuments(
    @Param('id', ParseIntPipe) id: number,
    @Body() documentsDto: { documentTypes: string[] },
  ): Promise<any> {
    // This would integrate with document generation service
    // For now, return a placeholder response
    return {
      message: 'Document generation initiated',
      employeeId: id,
      documentTypes: documentsDto.documentTypes,
    };
  }

  @Get(':id/assets')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async getEmployeeAssets(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: UserRequestI,
  ): Promise<any[]> {
    const currentUserId = req.user.id;
    return this.usersService.getEmployeeAssets(id, currentUserId);
  }

  @Post(':id/assign-asset/:assetId')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async assignAssetToEmployee(
    @Param('id', ParseIntPipe) id: number,
    @Param('assetId', ParseIntPipe) assetId: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.assignAssetToEmployee(id, assetId, currentUserId);
  }

  @Delete('unassign-asset/:assetId')
  @CanEdit(PLATFORM_SECTION_ENUM.EMPLOYEES)
  @ThrottleRelaxed()
  async unassignAssetFromEmployee(
    @Param('assetId', ParseIntPipe) assetId: number,
    @Req() req: UserRequestI,
  ): Promise<any> {
    const currentUserId = req.user.id;
    return this.usersService.unassignAssetFromEmployee(assetId, currentUserId);
  }
}

import {
  ConflictException,
  ForbiddenException,
  HttpException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { Op } from 'sequelize';
import { User } from 'src/models/users-models/user.model';
import {
  CreateEmployeeEmploymentInfoDto,
  CreateEmployeePersonalDetailsDto,
  CreateUserDto,
  UpdateUserDto,
} from '../dto';
import { CreateCandidateDto } from '../dto/create-candidate.dto';
import { CreateInterviewDetailsDto } from '../dto/create-interview-details.dto';
import * as bcrypt from 'bcrypt';
import { UserRoleService } from 'src/modules/roles-and-permissions/user-role/user-role.service';
import { RedirectSectionEnum } from 'src/utils/redirect-section.enum';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { ROLES_ENUM } from 'src/modules/roles-and-permissions/roles/utils/enums';
import { Sequelize } from 'sequelize-typescript';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from 'src/config/config.interface';
import { FilesService } from 'src/modules/files/srevices/files.service';
import { EmailService } from 'src/modules/email/email.service';
import { SendInterviewInvitationDto } from '../dto/send-interview-invitation.dto';
import { SendInvitationOnlyDto } from '../dto/send-invitation-only.dto';
import { USER_ACCOUNT_STATUS_ENUM } from 'src/utils/enums';
import { Candidate } from 'src/models/users-models/candidate.model';
import { CandidateStatus } from '../enums/invitation-status.enum';
import { CandidateFlowStep } from '../enums/candidate-flow-step.enum';
import { Company } from 'src/models/company.model';
import { UpdateCandidateDto } from '../dto/update-candidate.dto';
import { EmployeeFlowStep } from '../enums/employee-flow-step.enum';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(EmploymentDetails)
    private employeeDetailsModel: typeof EmploymentDetails,
    @InjectModel(Company)
    private companyModel: typeof Company,
    private readonly crudHelperService: CrudHelperService,
    private readonly userRoleService: UserRoleService,
    private readonly sequelize: Sequelize,
    private readonly configService: ConfigService<AppConfig>,
    private readonly filesService: FilesService,
    private readonly emailService: EmailService,
  ) {}

  //platform
  async findAll(): Promise<Partial<User>[]> {
    return this.crudHelperService.findAll<User>(this.userModel);
  }

  async findOne(id: number): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { id },
    });
  }

  async findOneWithRefreshToken(id: number): Promise<Partial<User>> {
    return this.userModel.scope('withRefreshToken').findOne({
      where: { id },
    });
  }

  async findByUsername(username: string): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { username },
    });
  }

  async findByEmail(email: string): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { email },
    });
  }

  async findByEmailWithPassword(email: string): Promise<Partial<User>> {
    return this.userModel.scope('withPassword').findOne({
      where: { email },
    });
  }

  async findByGoogleId(googleId: string): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { googleId },
    });
  }

  async remove(id: number): Promise<void> {
    await this.crudHelperService.delete(this.userModel, {
      where: { id },
    });
  }

  async uploadProfileImage(
    file: any,
    token: string,
    userId: number,
  ): Promise<{ uploadResult: any }> {
    return this.filesService.uploadFile(file, token, userId);
  }

  async createUser(
    createUserDto: CreateUserDto,
    currentUserId?: number,
  ): Promise<Partial<User>> {
    return this.createUserWithTransaction(createUserDto, currentUserId);
  }

  async createUserWithTransaction(
    createUserDto: CreateUserDto,
    currentUserId?: number,
    transaction?: any,
  ): Promise<Partial<User>> {
    // If no transaction provided, create one
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Check for existing username and email (outside transaction for better performance)
      const [existingUsername, existingEmail] = await Promise.all([
        this.userModel.findOne({ where: { username: createUserDto.username } }),
        this.userModel.findOne({ where: { email: createUserDto.email } }),
      ]);

      if (existingUsername) {
        throw new ConflictException('Username already exists');
      }

      if (existingEmail) {
        throw new ConflictException('Email already exists');
      }

      if (!createUserDto.roleId) {
        throw new ConflictException('Role ID is required for user creation');
      }

      const defaultPassword =
        this.configService.get<AppConfig['user']>('user').defaultPassword;
      const rawPassword = createUserDto.password || defaultPassword;
      const hashedPassword = await this.hashPassword(rawPassword);

      const createdUser = await this.userModel.create(
        {
          email: createUserDto.email,
          username: createUserDto.username,
          password: hashedPassword,
          firstName: createUserDto.firstName,
          lastName: createUserDto.lastName,
          redirectTo:
            createUserDto.redirectTo ?? RedirectSectionEnum.CREATE_COMPANY,
        },
        { transaction },
      );

      await this.userRoleService.assignRoleWithTransaction(
        createdUser.id,
        createUserDto.roleId,
        transaction,
      );

      const hasEmploymentFields =
        createUserDto.position &&
        createUserDto.departmentId &&
        createUserDto.hireDate;

      if (currentUserId && hasEmploymentFields) {
        const currentUserData = await this.userModel.findOne({
          where: { id: currentUserId },
        });

        if (!currentUserData?.companyId) {
          throw new ConflictException('Company context is missing');
        }

        // Set companyId for the new user
        await createdUser.update(
          { companyId: currentUserData.companyId },
          { transaction },
        );

        await this.employeeDetailsModel.create(
          {
            userId: createdUser.id,
            position: createUserDto.position,
            departmentId: createUserDto.departmentId,
            hireDate: createUserDto.hireDate,
          },
          { transaction },
        );
      }

      // Commit transaction if we created it
      if (shouldCommit) {
        await transaction.commit();
      }

      return createdUser;
    } catch (error) {
      // Rollback transaction if we created it
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async updateUser(
    id: number,
    updateUserDto: UpdateUserDto,
    transaction?: any,
  ): Promise<Partial<User>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    // Use transaction-aware find operation
    await this.crudHelperService.findOne<User>(
      this.userModel,
      { where: { id } },
      transaction,
    );

    if (updateUserDto.password) {
      updateUserDto.password = await this.hashPassword(updateUserDto.password);
    }

    if (updateUserDto.username) {
      const existingUsername = await this.userModel.findOne({
        where: { username: updateUserDto.username },
        transaction,
      });
      if (existingUsername && existingUsername.id !== id) {
        throw new ConflictException('Username already exists');
      }
    }

    if (updateUserDto.email) {
      const existingEmail = await this.userModel.findOne({
        where: { email: updateUserDto.email },
        transaction,
      });
      if (existingEmail && existingEmail.id !== id) {
        throw new ConflictException('Email already exists');
      }
    }

    const updatedUser = await this.crudHelperService.update<User>(
      this.userModel,
      updateUserDto,
      {
        where: { id },
      },
      transaction,
    );

    if (shouldCommit) {
      await transaction.commit();
    }

    return updatedUser;
  }

  async getAllUsersPaginated(
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<User>>> {
    const result = await this.crudHelperService.paginateWithQuery<User>(
      this.userModel,
      {
        page,
        limit,
        attributes: [
          'id',
          'username',
          'email',
          'firstName',
          'lastName',
          'status',
          'profileImage',
        ],
        include: [
          {
            model: UserRole,
            include: [
              {
                model: Role,
                where: {
                  name: [
                    ROLES_ENUM.COMPANY_ADMIN,
                    ROLES_ENUM.COMPANY_SUPER_ADMIN,
                  ],
                },
              },
            ],
          },
          {
            model: EmploymentDetails,
            required: false,
            limit: 1,
          },
        ],
      },
    );

    // Transform employmentDetails from array to single object
    const transformedData = result.data.map((user) => {
      const userData = user.toJSON ? user.toJSON() : user;
      if (
        userData.employmentDetails &&
        Array.isArray(userData.employmentDetails)
      ) {
        userData.employmentDetails =
          userData.employmentDetails.length > 0
            ? userData.employmentDetails[0]
            : null;
      }
      return userData;
    });

    return {
      ...result,
      data: transformedData,
    };
  }

  async getUsersByCompanyPaginated(
    userId: number,
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<User>>> {
    const currentUser = await this.crudHelperService.findOne<User>(
      this.userModel,
      { where: { id: userId } },
    );

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    const result = await this.crudHelperService.paginateWithQuery<User>(
      this.userModel,
      {
        page,
        limit,
        attributes: [
          'id',
          'username',
          'email',
          'firstName',
          'lastName',
          'status',
          'profileImage',
        ],
        where: { companyId: currentUser.companyId },
        include: [
          {
            model: EmploymentDetails,
            required: false,
          },
          {
            model: UserRole,
            include: [
              {
                model: Role,
              },
            ],
          },
        ],
      },
    );

    // Transform employmentDetails from array to single object
    const transformedData = result.data.map((user) => {
      const userData = user.toJSON ? user.toJSON() : user;
      if (
        userData.employmentDetails &&
        Array.isArray(userData.employmentDetails)
      ) {
        userData.employmentDetails =
          userData.employmentDetails.length > 0
            ? userData.employmentDetails[0]
            : null;
      }
      return userData;
    });

    return {
      ...result,
      data: transformedData,
    };
  }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  async validatePassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async isAdmin(userId: number): Promise<boolean> {
    const admin = await this.userRoleService.findUserById(userId);
    return !!admin;
  }

  async storeUserRefreshToken(
    userId: number,
    refreshToken: string,
    transaction?: any,
  ): Promise<void> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    // Use transaction-aware find operation
    const user = await this.crudHelperService.findOne<User>(
      this.userModel,
      { where: { id: userId } },
      transaction,
    );

    if (!user) {
      throw new ConflictException('User not found');
    }

    await this.updateUser(
      user.id,
      {
        refreshToken: refreshToken,
      },
      transaction,
    );

    if (shouldCommit) {
      await transaction.commit();
    }
  }

  // Employee functions

  async generateEmployeeId(companyId: number): Promise<string> {
    // 1. Get the company
    const company = await this.companyModel.findOne({
      where: { id: companyId },
    });
    if (!company) throw new Error('Company not found');

    // 2. Get initials from company name
    const initials = company.name
      .split(' ')
      .map((word) => word[0])
      .join('')
      .toUpperCase();

    // 3. Find the last employee for this company
    const lastEmployee = await this.userModel.findOne({
      where: { companyId },
      order: [['createdAt', 'DESC']],
    });

    // 4. Extract the numeric part from the last employee's ID
    let lastNumber = 0;
    if (lastEmployee && lastEmployee.id) {
      const match = lastEmployee.id.toString().match(/\d+$/);
      if (match) lastNumber = parseInt(match[0], 10);
    }

    // 5. Increment and format the new ID
    const newNumber = lastNumber + 1;
    const employeeId = `${initials}${newNumber.toString().padStart(3, '0')}`;
    return employeeId;
  }

  // Create Employee Personal Details
  async createEmployeePersonalDetails(
    personalDetailsDto: CreateEmployeePersonalDetailsDto,
    currentUserId: number,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      if (!companyId) {
        throw new ConflictException(
          'Company ID is required for employee creation',
        );
      }

      // Check for existing email
      const existingUser = await this.userModel.findOne({
        where: { email: personalDetailsDto.email },
      });
      if (existingUser) {
        throw new ConflictException('Email already exists');
      }

      // Generate employee ID
      const employeeId = await this.generateEmployeeId(companyId);

      // Hash password if provided, otherwise generate temporary password
      const password = personalDetailsDto.password || 'TempPass@123';
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user with basic info
      const userData = {
        password: hashedPassword,
        firstName: personalDetailsDto.firstName,
        lastName: personalDetailsDto.lastName,
        middleName: personalDetailsDto.middleName,
        email: personalDetailsDto.email,
        companyId: companyId,
        accountStatus: USER_ACCOUNT_STATUS_ENUM.PENDING, // Set to pending until onboarding complete
      };

      const user = (await this.crudHelperService.create(
        this.userModel,
        userData,
        transaction,
      )) as any;

      // Create user details (personal info)
      const userDetailsData = {
        userId: user.id,
        nationality: personalDetailsDto.nationality,
        dob: personalDetailsDto.dob,
        gender: personalDetailsDto.gender,
        passportNumber: personalDetailsDto.passportNumber,
        maritalStatus: personalDetailsDto.maritalStatus,
      };

      await this.crudHelperService.create(
        this.sequelize.models['UserDetails'],
        userDetailsData,
        transaction,
      );

      // Create contact details
      const contactDetailsData = {
        userId: user.id,
        phone: personalDetailsDto.phone,
        whatsappNumber: personalDetailsDto.whatsappNumber,
      };

      await this.crudHelperService.create(
        this.sequelize.models['ContactDetails'],
        contactDetailsData,
        transaction,
      );

      // Create address
      const addressData = {
        userId: user.id,
        address: personalDetailsDto.address,
        city: personalDetailsDto.city,
        state: personalDetailsDto.state,
        zipCode: personalDetailsDto.zipCode,
        countryId: personalDetailsDto.countryId,
      };

      await this.crudHelperService.create(
        this.sequelize.models['Address'],
        addressData,
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: user.id,
        employeeId: employeeId,
        currentStep: EmployeeFlowStep.PERSONAL_DETAILS,
        nextStep: EmployeeFlowStep.EMPLOYMENT_INFO,
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Add Employee Employment Information
  async createEmployeeEmploymentInfo(
    userId: number,
    employmentInfoDto: CreateEmployeeEmploymentInfoDto,
    currentUserId: number,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Get employee ID from existing employment details or generate new one
      let employeeId = employmentInfoDto.employeeId;
      if (!employeeId) {
        const existingEmployment = await this.crudHelperService.findOne(
          this.sequelize.models['EmploymentDetails'],
          { where: { userId: userId } },
        );
        employeeId =
          (existingEmployment as any)?.employeeId ||
          (await this.generateEmployeeId(companyId));
      }

      // Create or update employment details
      const employmentDetailsData = {
        userId: userId,
        employeeId: employeeId,
        departmentId: employmentInfoDto.departmentId,
        positionId: employmentInfoDto.positionId,
        hireDate: employmentInfoDto.hireDate,
        typeOfContract: employmentInfoDto.typeOfContract,
        contractDuration: employmentInfoDto.contractDuration,
        typeOfEmployment: employmentInfoDto.typeOfEmployment,
        workingHours: employmentInfoDto.workingHours,
        workingDays: employmentInfoDto.workingDays,
        probationPeriod: employmentInfoDto.probationPeriod,
        probationEndDate: employmentInfoDto.probationEndDate,
        reportingManagerId: employmentInfoDto.reportingManagerId,
      };

      // Check if employment details already exist
      const existingEmployment = await this.crudHelperService.findOne(
        this.sequelize.models['EmploymentDetails'],
        { where: { userId: userId } },
      );

      if (existingEmployment) {
        await this.crudHelperService.update(
          this.sequelize.models['EmploymentDetails'],
          employmentDetailsData,
          { where: { userId: userId } },
          // transaction,
        );
      } else {
        await this.crudHelperService.create(
          this.sequelize.models['EmploymentDetails'],
          employmentDetailsData,
          // transaction,
        );
      }

      await this.userRoleService.assignRoleWithTransaction(
        employee.id,
        employmentInfoDto.roleId,
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: userId,
        employeeId: employeeId,
        currentStep: EmployeeFlowStep.EMPLOYMENT_INFO,
        nextStep: EmployeeFlowStep.COMPENSATION,
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Add Employee Compensation Information
  async createEmployeeCompensation(
    userId: number,
    compensationDto: any,
    currentUserId: number,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Create compensation record
      const compensationData = {
        userId: userId,
        effectiveFrom: compensationDto.effectiveFrom,
        reviewDate: compensationDto.reviewDate,
        currency: compensationDto.currency,
        frequency: compensationDto.frequency,
        totalSalary: compensationDto.totalSalary,
        basicSalary: compensationDto.basicSalary,
        housing: compensationDto.housing,
        transportation: compensationDto.transportation,
        others: compensationDto.others,
      };

      const compensation = (await this.crudHelperService.create(
        this.sequelize.models['Compensation'],
        compensationData,
        transaction,
      )) as any;

      // Note: Benefits are created separately via createEmployeeBenefit endpoint

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: userId,
        compensationId: compensation.id,
        currentStep: EmployeeFlowStep.COMPENSATION,
        nextStep: EmployeeFlowStep.BANKING_DETAILS,
        message: 'Employee compensation information added successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Add Employee Banking Details
  async createEmployeeBankInfo(
    userId: number,
    bankInfoDto: any,
    currentUserId: number,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Create bank info record
      const bankInfoData = {
        userId: userId,
        companyId: companyId,
        holderName: bankInfoDto.holderName,
        bankName: bankInfoDto.bankName,
        branchName: bankInfoDto.branchName,
        accountNumber: bankInfoDto.accountNumber,
        iban: bankInfoDto.iban,
        swiftCode: bankInfoDto.swiftCode,
        bankAddress: bankInfoDto.bankAddress,
        currency: bankInfoDto.currency,
        routingNumber: bankInfoDto.routingNumber,
        branchCode: bankInfoDto.branchCode,
        isPrimary: bankInfoDto.isPrimary || true, // First bank info is primary by default
      };

      const bankInfo = (await this.crudHelperService.create(
        this.sequelize.models['BankInfo'],
        bankInfoData,
        transaction,
      )) as any;

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: userId,
        bankInfoId: bankInfo.id,
        currentStep: EmployeeFlowStep.BANKING_DETAILS,
        nextStep: EmployeeFlowStep.COMPLETED,
        message: 'Employee banking details added successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Create Employee Benefit (separate from compensation)
  async createEmployeeBenefit(
    compensationId: number,
    benefitDto: any,
    currentUserId: number,
    transaction?: any,
  ): Promise<any> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify compensation exists and belongs to company user
      const compensation = await this.crudHelperService.findOne(
        this.sequelize.models['Compensation'],
        {
          where: { id: compensationId },
          include: [
            {
              model: this.userModel,
              as: 'user',
              where: { companyId: companyId },
            },
          ],
        },
      );

      if (!compensation) {
        throw new NotFoundException('Compensation record not found');
      }

      // Create benefit record
      const benefitData = {
        compensationId: compensationId,
        allowance: benefitDto.allowance,
        frequency: benefitDto.frequency,
        amount: benefitDto.amount,
      };

      const benefit = (await this.crudHelperService.create(
        this.sequelize.models['Benefit'],
        benefitData,
        transaction,
      )) as any;

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: benefit.id,
        compensationId: compensationId,
        currentStep: 'benefits',
        message: 'Employee benefit created successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Complete Employee Onboarding (Activate Employee)
  async completeEmployeeOnboarding(
    userId: number,
    currentUserId: number,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Activate the employee account
      await this.crudHelperService.update(
        this.userModel,
        { accountStatus: USER_ACCOUNT_STATUS_ENUM.ACTIVE },
        { where: { id: userId, companyId: companyId } },
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: userId,
        currentStep: 'completed',
        status: 'completed',
        message: 'Employee onboarding completed successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Get Employee Onboarding Status
  async getEmployeeOnboardingStatus(
    userId: number,
    currentUserId: number,
  ): Promise<any> {
    // Get current user's company for authorization
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });
    const companyId = currentUser?.companyId;

    // Get employee with all related data
    const employee = await this.crudHelperService.findOne(this.userModel, {
      where: { id: userId, companyId: companyId },
      include: [
        {
          model: this.sequelize.models['UserDetails'],
          as: 'userDetails',
        },
        {
          model: this.sequelize.models['ContactDetails'],
          as: 'contactDetails',
        },
        {
          model: this.sequelize.models['EmploymentDetails'],
          as: 'employmentDetails',
        },
        {
          model: this.sequelize.models['Address'],
          as: 'addresses',
        },
        {
          model: this.sequelize.models['BankInfo'],
          as: 'bankInfos',
        },
      ],
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Check completion status of each step
    const status = {
      personalDetails:
        !!(employee as any).userDetails?.length &&
        !!(employee as any).contactDetails?.length &&
        !!(employee as any).addresses?.length,
      employmentInfo: !!(employee as any).employmentDetails?.length,
      compensation: false, // Will check compensation separately
      bankingDetails: !!(employee as any).bankInfos?.length,
      completed:
        (employee as any).accountStatus === USER_ACCOUNT_STATUS_ENUM.ACTIVE,
    };

    // Check compensation
    const compensation = await this.crudHelperService.findOne(
      this.sequelize.models['Compensation'],
      { where: { userId: userId } },
    );
    status.compensation = !!compensation;

    // Determine current step
    let currentStep = 'personal-details';
    if (status.personalDetails && !status.employmentInfo)
      currentStep = 'employment-info';
    else if (status.employmentInfo && !status.compensation)
      currentStep = 'compensation';
    else if (status.compensation && !status.bankingDetails)
      currentStep = 'banking-details';
    else if (status.bankingDetails && !status.completed)
      currentStep = 'complete';
    else if (status.completed) currentStep = 'completed';

    return {
      employeeId: userId,
      currentStep,
      status,
      employee,
    };
  }

  // Update Employee Personal Details
  async updateEmployeePersonalDetails(
    userId: number,
    personalDetailsDto: any,
    currentUserId: number,
    transaction?: any,
  ): Promise<any> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Update user basic info
      const userUpdateData: any = {};
      if (personalDetailsDto.firstName)
        userUpdateData.firstName = personalDetailsDto.firstName;
      if (personalDetailsDto.lastName)
        userUpdateData.lastName = personalDetailsDto.lastName;
      if (personalDetailsDto.email)
        userUpdateData.email = personalDetailsDto.email;
      if (personalDetailsDto.username)
        userUpdateData.username = personalDetailsDto.username;

      if (Object.keys(userUpdateData).length > 0) {
        await this.crudHelperService.update(
          this.userModel,
          userUpdateData,
          { where: { id: userId, companyId: companyId } },
          transaction,
        );
      }

      // Update user details
      const userDetailsData = {
        nationality: personalDetailsDto.nationality,
        dob: personalDetailsDto.dob,
        gender: personalDetailsDto.gender,
        passportNumber: personalDetailsDto.passportNumber,
        maritalStatus: personalDetailsDto.maritalStatus,
      };

      await this.crudHelperService.update(
        this.sequelize.models['UserDetails'],
        userDetailsData,
        { where: { userId: userId } },
        transaction,
      );

      // Update contact details
      const contactDetailsData = {
        phone: personalDetailsDto.phone,
        whatsappNumber: personalDetailsDto.whatsappNumber,
        emergencyContact: personalDetailsDto.emergencyContact,
        emergencyContactRelation: personalDetailsDto.emergencyContactRelation,
        emergencyContactName: personalDetailsDto.emergencyContactName,
      };

      await this.crudHelperService.update(
        this.sequelize.models['ContactDetails'],
        contactDetailsData,
        { where: { userId: userId } },
        transaction,
      );

      // Update address
      const addressData = {
        address: personalDetailsDto.address,
        city: personalDetailsDto.city,
        state: personalDetailsDto.state,
        country: personalDetailsDto.country,
        pinCode: personalDetailsDto.pinCode,
      };

      await this.crudHelperService.update(
        this.sequelize.models['Address'],
        addressData,
        { where: { userId: userId } },
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: userId,
        currentStep: 'personal-details',
        message: 'Employee personal details updated successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Update Employee Employment Information
  async updateEmployeeEmploymentInfo(
    userId: number,
    employmentInfoDto: any,
    currentUserId: number,
    transaction?: any,
  ): Promise<any> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Update employment details
      const employmentDetailsData = {
        departmentId: employmentInfoDto.departmentId,
        positionId: employmentInfoDto.positionId,
        positionTitle: employmentInfoDto.positionTitle,
        designation: employmentInfoDto.designation,
        hireDate: employmentInfoDto.hireDate,
        typeOfContract: employmentInfoDto.typeOfContract,
        contractDuration: employmentInfoDto.contractDuration,
        typeOfEmployment: employmentInfoDto.typeOfEmployment,
        workingHours: employmentInfoDto.workingHours,
        workingDays: employmentInfoDto.workingDays,
        probationPeriod: employmentInfoDto.probationPeriod,
        probationEndDate: employmentInfoDto.probationEndDate,
        reportingManagerId: employmentInfoDto.reportingManagerId,
        companyEmail: employmentInfoDto.companyEmail,
        companyPhoneNumber: employmentInfoDto.companyPhoneNumber,
      };

      await this.crudHelperService.update(
        this.sequelize.models['EmploymentDetails'],
        employmentDetailsData,
        { where: { userId: userId } },
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: userId,
        currentStep: 'employment-info',
        message: 'Employee employment information updated successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Update Employee Compensation
  async updateEmployeeCompensation(
    userId: number,
    compensationDto: any,
    currentUserId: number,
    transaction?: any,
  ): Promise<any> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Update compensation record
      const compensationData = {
        effectiveFrom: compensationDto.effectiveFrom,
        reviewDate: compensationDto.reviewDate,
        currency: compensationDto.currency,
        frequency: compensationDto.frequency,
        totalSalary: compensationDto.totalSalary,
        basicSalary: compensationDto.basicSalary,
        housing: compensationDto.housing,
        transportation: compensationDto.transportation,
        others: compensationDto.others,
      };

      await this.crudHelperService.update(
        this.sequelize.models['Compensation'],
        compensationData,
        { where: { userId: userId } },
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: userId,
        currentStep: 'compensation',
        message: 'Employee compensation updated successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Update Employee (Legacy - uses nested objects - DEPRECATED)
  async updateEmployee(
    userId: number,
    updateEmployeeDto: any,
    currentUserId: number,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Find the employee to update
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Update user basic info
      if (updateEmployeeDto.basicInfo) {
        const userUpdateData: any = {};
        if (updateEmployeeDto.basicInfo.firstName)
          userUpdateData.firstName = updateEmployeeDto.basicInfo.firstName;
        if (updateEmployeeDto.basicInfo.lastName)
          userUpdateData.lastName = updateEmployeeDto.basicInfo.lastName;
        if (updateEmployeeDto.basicInfo.email)
          userUpdateData.email = updateEmployeeDto.basicInfo.email;
        if (updateEmployeeDto.basicInfo.username)
          userUpdateData.username = updateEmployeeDto.basicInfo.username;

        if (Object.keys(userUpdateData).length > 0) {
          await this.crudHelperService.update(
            this.userModel,
            { id: userId },
            userUpdateData,
            transaction,
          );
        }
      }

      // Update user details
      if (updateEmployeeDto.personalInfo) {
        const userDetailsData = {
          nationality: updateEmployeeDto.personalInfo.nationality,
          dob: updateEmployeeDto.personalInfo.dob,
          gender: updateEmployeeDto.personalInfo.gender,
          passportNumber: updateEmployeeDto.personalInfo.passportNumber,
          maritalStatus: updateEmployeeDto.personalInfo.maritalStatus,
        };

        await this.crudHelperService.update(
          this.sequelize.models['UserDetails'],
          userDetailsData,
          { where: { userId: userId } },
          transaction,
        );
      }

      // Update contact details
      if (updateEmployeeDto.contactInfo) {
        const contactDetailsData = {
          phone: updateEmployeeDto.contactInfo.phone,
          whatsappNumber: updateEmployeeDto.contactInfo.whatsappNumber,
          emergencyContact: updateEmployeeDto.contactInfo.emergencyContact,
          emergencyContactRelation:
            updateEmployeeDto.contactInfo.emergencyContactRelation,
          emergencyContactName:
            updateEmployeeDto.contactInfo.emergencyContactName,
        };

        await this.crudHelperService.update(
          this.sequelize.models['ContactDetails'],
          contactDetailsData,
          { where: { userId: userId } },
          transaction,
        );
      }

      // Update employment details
      if (updateEmployeeDto.employmentInfo) {
        const employmentDetailsData = {
          departmentId: updateEmployeeDto.employmentInfo.departmentId,
          position: updateEmployeeDto.employmentInfo.position,
          designation: updateEmployeeDto.employmentInfo.designation,
          hireDate: updateEmployeeDto.employmentInfo.hireDate,
          typeOfContract: updateEmployeeDto.employmentInfo.typeOfContract,
          contractDuration: updateEmployeeDto.employmentInfo.contractDuration,
          typeOfEmployment: updateEmployeeDto.employmentInfo.typeOfEmployment,
          workingHours: updateEmployeeDto.employmentInfo.workingHours,
          workingDays: updateEmployeeDto.employmentInfo.workingDays,
          probationPeriod: updateEmployeeDto.employmentInfo.probationPeriod,
          probationEndDate: updateEmployeeDto.employmentInfo.probationEndDate,
          reportingManagerId:
            updateEmployeeDto.employmentInfo.reportingManagerId,
          companyEmail: updateEmployeeDto.employmentInfo.companyEmail,
          companyPhoneNumber:
            updateEmployeeDto.employmentInfo.companyPhoneNumber,
        };

        await this.crudHelperService.update(
          this.sequelize.models['EmploymentDetails'],
          employmentDetailsData,
          { where: { userId: userId } },
          transaction,
        );
      }

      // Update address
      if (updateEmployeeDto.address) {
        const addressData = {
          address: updateEmployeeDto.address.address,
          city: updateEmployeeDto.address.city,
          state: updateEmployeeDto.address.state,
          country: updateEmployeeDto.address.country,
          pinCode: updateEmployeeDto.address.pinCode,
        };

        await this.crudHelperService.update(
          this.sequelize.models['Address'],
          addressData,
          { where: { userId: userId } },
          transaction,
        );
      }

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: userId,
        message: 'Employee updated successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async getEmployeeDetails(
    userId: number,
    currentUserId: number,
  ): Promise<any> {
    // Step 1: Get current user to confirm company access
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
      attributes: ['id', 'companyId'],
    });

    const companyId = currentUser?.companyId;
    if (!companyId) {
      throw new UnauthorizedException('Company access not found');
    }

    // Step 2: Fetch employee and only populate valid associations
    const employee = await this.crudHelperService.findOne(this.userModel, {
      where: { id: userId, companyId },
      attributes: [
        'id',
        'username',
        'email',
        'firstName',
        'lastName',
        'status',
        'timeZone',
        'isEmailVerified',
        'createdAt',
        'updatedAt',
      ],
      include: [
        {
          model: this.sequelize.models['UserDetails'],
          as: 'userDetails',
          attributes: ['dob', 'gender', 'maritalStatus', 'nationality'],
          required: false,
        },
        {
          model: this.sequelize.models['ContactDetails'],
          as: 'contactDetails',
          attributes: ['phone', 'whatsappNumber'],
          required: false,
        },
        {
          model: this.sequelize.models['EmploymentDetails'],
          as: 'employmentDetails',
          attributes: ['id'], // Only include columns you know exist
          required: false,
          include: [
            {
              model: this.sequelize.models['Department'],
              as: 'department',
              attributes: ['id', 'name'],
              required: false,
            },
            {
              model: this.sequelize.models['Position'],
              as: 'position',
              attributes: ['id', 'title'],
              required: false,
            },
          ],
        },
        {
          model: this.sequelize.models['Address'],
          as: 'addresses',
          attributes: ['countryId', 'state', 'city', 'zipCode'],
          required: false,
        },
        {
          model: this.sequelize.models['BankInfo'],
          as: 'bankInfos',
          attributes: ['bankName', 'accountNumber'],
          required: false,
        },
        {
          model: this.sequelize.models['UserRole'],
          as: 'userRoles',
          attributes: ['id'],
          required: false,
          include: [
            {
              model: this.sequelize.models['Role'],
              as: 'role',
              attributes: ['id', 'name'],
              required: false,
            },
          ],
        },
      ],
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    return employee;
  }

  // Update Employee Banking Details
  async updateEmployeeBankInfo(
    bankInfoId: number,
    bankInfoDto: any,
    currentUserId: number,
    transaction?: any,
  ): Promise<any> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Find and verify bank info belongs to company
      const bankInfo = await this.crudHelperService.findOne(
        this.sequelize.models['BankInfo'],
        {
          where: { id: bankInfoId, companyId: companyId },
        },
      );

      if (!bankInfo) {
        throw new NotFoundException('Bank information not found');
      }

      // Update bank info
      const updateData = {
        holderName: bankInfoDto.holderName,
        bankName: bankInfoDto.bankName,
        branchName: bankInfoDto.branchName,
        accountNumber: bankInfoDto.accountNumber,
        iban: bankInfoDto.iban,
        swiftCode: bankInfoDto.swiftCode,
        bankAddress: bankInfoDto.bankAddress,
        currency: bankInfoDto.currency,
        routingNumber: bankInfoDto.routingNumber,
        branchCode: bankInfoDto.branchCode,
        isPrimary: bankInfoDto.isPrimary,
      };

      await this.crudHelperService.update(
        this.sequelize.models['BankInfo'],
        updateData,
        { where: { id: bankInfoId, companyId: companyId } },
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        id: bankInfoId,
        message: 'Employee bank information updated successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Get Employee List with pagination for company
  async getEmployeesByCompanyPaginated(
    currentUserId: number,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResult<Partial<User>>> {
    // Get current user's company
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });
    const companyId = currentUser?.companyId;

    if (!companyId) {
      throw new ConflictException('Company ID is required');
    }

    return this.crudHelperService.paginateWithQuery<User>(this.userModel, {
      page,
      limit,
      where: { companyId: companyId },
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: this.sequelize.models['EmploymentDetails'],
          as: 'employmentDetails',
          include: [
            {
              model: this.sequelize.models['Department'],
              as: 'department',
            },
            {
              model: this.sequelize.models['Position'],
              as: 'position',
            },
          ],
        },
        {
          model: this.sequelize.models['UserDetails'],
          as: 'userDetails',
        },
        {
          model: this.sequelize.models['UserRole'],
          as: 'userRoles',
          include: [
            {
              model: this.sequelize.models['Role'],
              as: 'role',
            },
          ],
        },
      ],
    });
  }

  // Get Candidate Summary
  async getCandidateSummary(currentUserId: number): Promise<any> {
    // Get current user's company
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });
    const companyId = currentUser?.companyId;

    if (!companyId) {
      throw new ConflictException('Company ID is required');
    }

    // Get total candidates count
    const totalCandidates = await this.crudHelperService.count(
      this.sequelize.models['Candidate'],
      { where: { companyId: companyId } },
    );

    // Get candidates with offer letters sent
    const offerLettersSent = await this.crudHelperService.count(
      this.sequelize.models['Candidate'],
      {
        where: {
          companyId: companyId,
          status: 'OFFER_SENT',
        },
      },
    );

    // Get candidates with offer letters accepted
    const offerLettersAccepted = await this.crudHelperService.count(
      this.sequelize.models['Candidate'],
      {
        where: {
          companyId: companyId,
          status: 'OFFER_ACCEPTED',
        },
      },
    );

    // Get candidates with offer letters rejected
    const offerLettersRejected = await this.crudHelperService.count(
      this.sequelize.models['Candidate'],
      {
        where: {
          companyId: companyId,
          status: 'OFFER_REJECTED',
        },
      },
    );

    // Get additional counts
    const pendingInterviews = await this.crudHelperService.count(
      this.sequelize.models['Candidate'],
      {
        where: {
          companyId: companyId,
          status: 'INTERVIEW_SCHEDULED',
        },
      },
    );

    const newApplications = await this.crudHelperService.count(
      this.sequelize.models['Candidate'],
      {
        where: {
          companyId: companyId,
          status: 'APPLIED',
        },
      },
    );

    // Return data with fallback to dummy data if no real data exists
    return {
      totalCandidates: totalCandidates || 7,
      offerLettersSent: offerLettersSent || 3,
      offerLettersAccepted: offerLettersAccepted || 3,
      offerLettersRejected: offerLettersRejected || 1,
      pendingInterviews: pendingInterviews || 2,
      newApplications: newApplications || 5,
      // Additional summary metrics
      interviewsCompleted:
        (await this.crudHelperService.count(
          this.sequelize.models['Candidate'],
          {
            where: {
              companyId: companyId,
              status: 'INTERVIEW_COMPLETED',
            },
          },
        )) || 4,
      candidatesRejected:
        (await this.crudHelperService.count(
          this.sequelize.models['Candidate'],
          {
            where: {
              companyId: companyId,
              status: 'REJECTED',
            },
          },
        )) || 2,
      // Summary percentages (calculated from dummy data if no real data)
      offerAcceptanceRate:
        totalCandidates > 0
          ? Math.round(
              (offerLettersAccepted / Math.max(offerLettersSent, 1)) * 100,
            )
          : 100, // 100% acceptance rate for dummy data
      interviewToOfferRate:
        totalCandidates > 0
          ? Math.round(
              ((offerLettersSent + offerLettersAccepted) /
                Math.max(totalCandidates, 1)) *
                100,
            )
          : 75, // 75% for dummy data
    };
  }

  // Get Company Positions
  async getCompanyPositions(currentUserId: number): Promise<any[]> {
    // Get current user's company
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });
    const companyId = currentUser?.companyId;

    if (!companyId) {
      throw new ConflictException('Company ID is required');
    }

    return this.crudHelperService.findAll(this.sequelize.models['Position'], {
      where: { companyId: companyId },
      order: [['title', 'ASC']],
      include: [
        {
          model: this.sequelize.models['Department'],
          as: 'department',
        },
      ],
    });
  }

  // Get Company Roles
  async getCompanyRoles(currentUserId: number): Promise<any[]> {
    // Get current user's company
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });
    const companyId = currentUser?.companyId;

    if (!companyId) {
      throw new ConflictException('Company ID is required');
    }

    return this.crudHelperService.findAll(this.sequelize.models['Role'], {
      where: { companyId: companyId },
      order: [['name', 'ASC']],
    });
  }

  // Assign Asset to Employee
  async assignAssetToEmployee(
    userId: number,
    assetId: number,
    currentUserId: number,
    transaction?: any,
  ): Promise<any> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Verify asset exists and belongs to company
      const asset = await this.crudHelperService.findOne(
        this.sequelize.models['Asset'],
        {
          where: { id: assetId, companyId: companyId },
        },
      );

      if (!asset) {
        throw new NotFoundException('Asset not found');
      }

      // Update asset to assign to employee
      await this.crudHelperService.update(
        this.sequelize.models['Asset'],
        { assignedTo: userId, status: 'IN_USE' },
        { where: { id: assetId, companyId: companyId } },
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        message: 'Asset assigned to employee successfully',
        assetId: assetId,
        employeeId: userId,
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Unassign Asset from Employee
  async unassignAssetFromEmployee(
    assetId: number,
    currentUserId: number,
    transaction?: any,
  ): Promise<any> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify asset exists and belongs to company
      const asset = await this.crudHelperService.findOne(
        this.sequelize.models['Asset'],
        {
          where: { id: assetId, companyId: companyId },
        },
      );

      if (!asset) {
        throw new NotFoundException('Asset not found');
      }

      // Update asset to unassign from employee
      await this.crudHelperService.update(
        this.sequelize.models['Asset'],
        { assignedTo: null, status: 'AVAILABLE' },
        { where: { id: assetId, companyId: companyId } },
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        message: 'Asset unassigned from employee successfully',
        assetId: assetId,
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Get Employee Assets
  async getEmployeeAssets(
    userId: number,
    currentUserId: number,
  ): Promise<any[]> {
    // Get current user's company for authorization
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });
    const companyId = currentUser?.companyId;

    // Verify employee exists and belongs to company
    const employee = await this.userModel.findOne({
      where: { id: userId, companyId: companyId },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Get assets assigned to employee
    const assets = await this.crudHelperService.findAll(
      this.sequelize.models['Asset'],
      {
        where: { assignedTo: userId, companyId: companyId },
        order: [['createdAt', 'DESC']],
      },
    );

    return assets;
  }

  // Delete Employee (soft delete by deactivating)
  async deleteEmployee(
    userId: number,
    currentUserId: number,
    transaction?: any,
  ): Promise<any> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company for authorization
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;

      // Verify employee exists and belongs to company
      const employee = await this.userModel.findOne({
        where: { id: userId, companyId: companyId },
      });

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      // Soft delete by setting account status to inactive
      await this.crudHelperService.update(
        this.userModel,
        { accountStatus: USER_ACCOUNT_STATUS_ENUM.INACTIVE },
        { where: { id: userId, companyId: companyId } },
        transaction,
      );

      // Unassign all assets from employee
      await this.crudHelperService.update(
        this.sequelize.models['Asset'],
        { assignedTo: null, status: 'AVAILABLE' },
        { where: { assignedTo: userId, companyId: companyId } },
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        message: 'Employee deactivated successfully',
        employeeId: userId,
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  // Candidate functions

  async createCandidateWithTransaction(
    createCandidateDto: CreateCandidateDto,
    currentUserId: number,
    files?: { resume?: any; passport?: any }, // Object containing both file types
    token?: string,
    transaction?: any,
  ): Promise<Partial<any>> {
    // If no transaction provided, create one
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Check for existing email using crudHelperService
      const existingEmail = await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: { email: createCandidateDto.email },
        },
      );
      if (existingEmail) {
        throw new ConflictException('Email already exists');
      }
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;
      if (!companyId) {
        throw new ConflictException(
          'Company ID is required for candidate creation',
        );
      }

      if (!companyId) {
        throw new ConflictException(
          'Company ID is required for candidate creation',
        );
      }

      console.log(createCandidateDto);

      // First, create the candidate without file URLs
      const createdCandidate = await this.crudHelperService.create(
        this.sequelize.models['Candidate'],
        {
          email: createCandidateDto.email,
          firstName: createCandidateDto.firstName,
          middleName: createCandidateDto.middleName || '',
          lastName: createCandidateDto.lastName,
          gender: createCandidateDto.gender || '',
          positionId: createCandidateDto.positionId || null,
          departmentId: createCandidateDto.departmentId || 0,
          phone: createCandidateDto.phone || '',
          passportNumber: createCandidateDto.passportNumber || '',
          passportExpiryDate: createCandidateDto.passportExpiryDate || '',
          companyId: companyId,
          countryId: createCandidateDto.countryId || 0,
          resumeURL: '', // Initially empty, will be updated after file upload
          passportURL: '', // Initially empty, will be updated after file upload
        },
        transaction,
      );

      // Now upload the files using the candidate's ID
      let resumeURL = '';
      let passportURL = '';
      let resumeFileName = '';
      let passportFileName = '';
      const candidateId = (createdCandidate as any).id;

      // Upload resume file if provided
      if (files?.resume && token && candidateId) {
        const resumeUploadResult = await this.filesService.uploadFile(
          files.resume,
          token,
          candidateId, // Use the created candidate's ID
        );
        resumeURL = resumeUploadResult.uploadResult.data.fullUrl || '';
        resumeFileName =
          resumeUploadResult.uploadResult.data.originalName || '';
        console.log(resumeUploadResult, 'passportURL');
      }

      // Upload passport file if provided
      if (files?.passport && token && candidateId) {
        const passportUploadResult = await this.filesService.uploadFile(
          files.passport,
          token,
          candidateId, // Use the created candidate's ID
        );
        passportURL = passportUploadResult.uploadResult.data.fullUrl || '';
        passportFileName =
          passportUploadResult.uploadResult.data.originalName || '';
      }
      console.log(resumeURL, 'resumeURL');
      // Update the candidate with the file URLs if any were uploaded
      if (resumeURL || passportURL) {
        const updateData: any = {};
        if (resumeURL) updateData.resumeURL = resumeURL;
        if (passportURL) updateData.passportURL = passportURL;

        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          updateData,
          { where: { id: candidateId } },
          transaction,
        );

        await this.updateCandidateFlowStep(candidateId, transaction);

        // Update the local object to reflect the changes
        if (resumeURL) (createdCandidate as any).resumeURL = resumeURL;
        if (passportURL) (createdCandidate as any).passportURL = passportURL;
        if (resumeFileName)
          (createdCandidate as any).resumeFileName = resumeFileName;
        if (passportFileName)
          (createdCandidate as any).passportFileName = passportFileName;
      }

      // Commit transaction if we created it
      if (shouldCommit) {
        await transaction.commit();
      }

      return createdCandidate;
    } catch (error) {
      // Rollback transaction if we created it
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async updateCandidateWithTransaction(
    candidateId: number,
    updateCandidateDto: UpdateCandidateDto,
    currentUserId: number,
    files?: { resume?: any; passport?: any },
    token?: string,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      const candidate = (await this.sequelize.models['Candidate'].findOne({
        where: { id: candidateId },
      })) as any;

      if (!candidate) {
        throw new NotFoundException('Candidate not found');
      }

      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });

      if (!currentUser || !currentUser.companyId) {
        throw new ForbiddenException('User does not belong to any company');
      }

      if (candidate.companyId !== currentUser.companyId) {
        throw new ForbiddenException(
          'You do not have permission to update this candidate',
        );
      }

      // Update basic fields
      await this.crudHelperService.update(
        this.sequelize.models['Candidate'],
        updateCandidateDto,
        { where: { id: candidateId } },
        transaction,
      );

      // Upload files if provided
      let resumeURL = '';
      let passportURL = '';

      if (files?.resume && token) {
        const resumeUpload = await this.filesService.uploadFile(
          files.resume,
          token,
          candidateId,
        );
        resumeURL = resumeUpload.uploadResult.data.fullUrl || '';
      }

      if (files?.passport && token) {
        const passportUpload = await this.filesService.uploadFile(
          files.passport,
          token,
          candidateId,
        );
        passportURL = passportUpload.uploadResult.data.fullUrl || '';
      }

      const fileUpdate: any = {};
      if (resumeURL) fileUpdate.resumeURL = resumeURL;
      if (passportURL) fileUpdate.passportURL = passportURL;

      if (Object.keys(fileUpdate).length > 0) {
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          fileUpdate,
          { where: { id: candidateId } },
          transaction,
        );
      }

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        ...candidate.toJSON(),
        ...updateCandidateDto,
        ...fileUpdate,
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async removeCandidate(id: number, currentUserId: number): Promise<void> {
    await this.crudHelperService.delete(this.sequelize.models['Candidate'], {
      where: { id, companyId: currentUserId },
    });
  }

  async sendInterviewInvitationAndSave(
    invitationData: SendInterviewInvitationDto,
    currentUserId: number,
    transaction?: any,
  ): Promise<{ success: boolean; message: string; interviewDetails?: any }> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company information
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
        include: [
          {
            model: this.sequelize.models['Company'],
            as: 'company',
          },
        ],
      });

      const candidate = (await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: { id: invitationData.candidateId },
          include: [
            {
              model: this.sequelize.models['Position'],
              attributes: ['id', 'title'],
            },
          ],
        },
      )) as Candidate;

      if (!candidate) {
        throw new NotFoundException('Candidate not found');
      }

      const interviwer = (await this.userModel.findOne({
        where: { id: invitationData.interviewerId },
      })) as User;
      if (!interviwer) {
        throw new NotFoundException('Interviewer not found');
      }

      if (!interviwer) {
        throw new NotFoundException('Interviewer not found');
      }

      // Check if interview details already exist for this candidate
      const existingInterviewDetails = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        {
          where: { candidateId: (candidate as any).id },
        },
      );

      const interviewData = {
        candidateId: (candidate as any).id,
        interviewerId: invitationData.interviewerId,
        interviewDate: new Date(invitationData.interviewDate),
        interviewTime: invitationData.interviewTime,
        interviewType: invitationData.interviewType,
        interviewMode: invitationData.interviewMode,
        location: invitationData.location,
        meetingLink: invitationData.meetingLink,
        customMessage: invitationData.customMessage || '',
      };

      if (existingInterviewDetails) {
        // Update existing interview details
        await this.crudHelperService.update(
          this.sequelize.models['InterviewDetails'],
          interviewData,
          { where: { candidateId: (candidate as any).id } },
          transaction,
        );
      } else {
        // Create new interview details record
        await this.crudHelperService.create(
          this.sequelize.models['InterviewDetails'],
          interviewData,
          transaction,
        );
      }

      const company = (currentUser as any).company;

      // Send interview invitation email if isToBeSent is true
      if (invitationData.isToBeSent) {
        const invitationEmailData = {
          candidateEmail: candidate.email,
          candidateFirstName: candidate.firstName,
          candidateLastName: candidate.lastName,
          interviewDate: invitationData.interviewDate,
          interviewTime: invitationData.interviewTime,
          interviewMode: invitationData.interviewMode,
          interviewType: invitationData.interviewType,
          location: invitationData.location || 'Office Location',
          meetingLink: invitationData.meetingLink || '',
          interviewerName: `${interviwer.firstName} ${interviwer.lastName}`,
          position: candidate.position?.title || 'Position',
          companyName: company?.name || 'Company',
          hrName: `${currentUser.firstName} ${currentUser.lastName}`,
          hrEmail: currentUser.email,
          customMessage: invitationData.customMessage || '',
          // Add commonly used template variables
          date: new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          }),
          currentDate: new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          }),
          year: new Date().getFullYear(),
          companyAddress: company?.address || 'Company Address',
          companyPhone: company?.phone || 'Company Phone',
          companyEmail: company?.email || 'Company Email',
        };

        // Send email using Handlebars template
        await this.emailService.sendInterviewInvitation(invitationEmailData);

        // Update candidate status to SENT only after email is successfully sent
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          { status: CandidateStatus.INTERVIEW_SCHEDULED },
          { where: { id: (candidate as any).id } },
          transaction,
        );
      }

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        success: true,
        message: invitationData.isToBeSent
          ? 'Interview invitation sent successfully'
          : 'Interview details saved successfully',
        interviewDetails: existingInterviewDetails ? 'updated' : 'created',
      };
    } catch (error) {
      console.log('the error comes from here', error);
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async sendInvitationOnly(
    sendInvitationDto: SendInvitationOnlyDto,
    currentUserId: number,
    transaction?: any,
  ): Promise<{ success: boolean; message: string }> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company information
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
        include: [
          {
            model: this.sequelize.models['Company'],
            as: 'company',
          },
        ],
      });

      if (!currentUser?.companyId) {
        throw new ConflictException('User is not associated with any company');
      }

      // Get the interview details with candidate and interviewer information
      const interviewDetails = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        {
          where: { id: sendInvitationDto.interviewDetailsId },
          include: [
            {
              model: this.sequelize.models['Candidate'],
              where: { companyId: currentUser.companyId }, // Ensure candidate belongs to same company
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
            {
              model: this.userModel,
              as: 'interviewer',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      );

      console.log(interviewDetails);

      if (!interviewDetails) {
        throw new NotFoundException(
          'Interview details not found or not authorized',
        );
      }

      const candidate = (interviewDetails as any).candidate;
      const interviewer = (interviewDetails as any).interviewer;
      const company = (currentUser as any).company || { name: 'Company' };

      // Use custom message from DTO if provided, otherwise use the one from interview details
      const customMessage =
        sendInvitationDto.customMessage ||
        (interviewDetails as any).customMessage;

      // Send interview invitation email
      const invitationEmailData = {
        candidateEmail: candidate.email,
        candidateFirstName: candidate.firstName,
        candidateLastName: candidate.lastName,
        interviewDate: (interviewDetails as any)?.interviewDate || 'TBD',
        interviewTime: (interviewDetails as any)?.interviewTime || 'TBD',
        interviewMode: (interviewDetails as any)?.interviewMode || 'Online',
        interviewType: (interviewDetails as any)?.interviewType || 'Interview',
        location: (interviewDetails as any)?.location || 'Office Location',
        meetingLink: (interviewDetails as any)?.meetingLink || '',
        interviewerName: `${interviewer.firstName} ${interviewer.lastName}`,
        position: candidate.position || 'Position',
        companyName: company.name,
        hrName: `${currentUser.firstName} ${currentUser.lastName}`,
        hrEmail: currentUser.email,
        customMessage: customMessage,
        // Add commonly used template variables
        date: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        currentDate: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        year: new Date().getFullYear(),
        companyAddress: company?.address || 'Company Address',
        companyPhone: company?.phone || 'Company Phone',
        companyEmail: company?.email || 'Company Email',
      };

      // Send email using Handlebars template
      await this.emailService.sendInterviewInvitation(invitationEmailData);

      // Update the candidate status to SENT and interview details custom message
      const interviewDetailsForUpdate = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        { where: { id: sendInvitationDto.interviewDetailsId } },
      );

      if (interviewDetailsForUpdate) {
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          { status: CandidateStatus.INTERVIEW_SCHEDULED },
          { where: { id: (interviewDetailsForUpdate as any).candidateId } },
          transaction,
        );

        // Update custom message if provided
        if (customMessage) {
          await this.crudHelperService.update(
            this.sequelize.models['InterviewDetails'],
            { customMessage: customMessage },
            { where: { id: sendInvitationDto.interviewDetailsId } },
            transaction,
          );
        }
      }

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        success: true,
        message: 'Interview invitation sent successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async generatePdfFromTemplateAndUpload(
    templateName: string,
    token: string,
    userId: number,
    data: any,
  ) {
    const pdfBuffer = await this.emailService.generatePdfFromTemplate(
      templateName,
      data,
    );

    const file = {
      buffer: Buffer.from(pdfBuffer), // Ensure it's a real Buffer
      originalname: `${templateName}-${Date.now()}.pdf`,
    };

    // If no token provided, try to generate one or use a default approach
    if (!token) {
      // For now, we'll skip the upload if no token is provided
      // In a real implementation, you might want to generate a token or handle this differently
      console.warn('No token provided for file upload, skipping upload');
      return {
        uploadResult: {
          data: {
            fullUrl: `local://generated-pdfs/${file.originalname}`,
            originalName: file.originalname,
          },
        },
      };
    }

    const uploadResult = await this.filesService.uploadFile(
      file,
      token,
      userId,
    );
    return uploadResult; // Return file metadata or URL if needed
  }

  async sendOfferLetter(
    candidateId: number,
    currentUserId: number,
    token?: string,
    transaction?: any,
  ): Promise<{ success: boolean; message: string }> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company information
      const currentUser = await this.crudHelperService.findOne<User>(
        this.userModel,
        {
          where: { id: currentUserId },
          include: [
            {
              model: this.sequelize.models['Company'],
              as: 'company',
            },
          ],
        },
      );

      if (!currentUser?.companyId) {
        throw new ConflictException('User is not associated with any company');
      }

      // Get candidate with compensation and benefits
      const candidate = await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: {
            id: candidateId,
            companyId: currentUser.companyId,
          },
          include: [
            {
              model: this.sequelize.models['Compensation'],
              as: 'compensation',
              include: [
                {
                  model: this.sequelize.models['Benefit'],
                  as: 'benefits',
                },
              ],
            },
            {
              model: this.sequelize.models['Country'],
              as: 'country',
              attributes: ['name'],
            },
          ],
        },
      );

      if (!candidate) {
        throw new NotFoundException('Candidate not found or not authorized');
      }

      const compensation = (candidate as any).compensation;
      // if (!compensation) {
      //   throw new NotFoundException(
      //     'Compensation details not found for candidate',
      //   );
      // }

      const company = (currentUser as any).company;
      // Generate automatic offer details
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + 30); // 30 days from now
      const formattedStartDate = startDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      const acceptanceDeadline = new Date();
      acceptanceDeadline.setDate(acceptanceDeadline.getDate() + 7); // 7 days from now
      const formattedDeadline = acceptanceDeadline.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      // Prepare offer letter data
      const offerData = {
        candidateEmail: (candidate as any).email,
        candidateFirstName: (candidate as any).firstName,
        candidateLastName: (candidate as any).lastName,
        position: (candidate as any).position || 'Position',
        companyName: company.name,
        companyEmail: company.email,
        companyPhone: company.phone,
        companyWebsite: company.website,
        companyAddress: company.address,
        startDate: formattedStartDate,
        employmentType: 'Full-time',
        location: company.address || 'Office Location',
        reportingTo: 'Department Manager',
        currency: compensation?.currency || 'AED',
        totalMonthlySalary: compensation?.totalSalary || 0,
        basicSalary: compensation?.basicSalary || 0,
        housing: compensation?.housing || 0,
        transportation: compensation?.transportation || 0,
        others: compensation?.others || 0,
        benefits: (compensation?.benefits || []).map((benefit: any) => ({
          allowance: benefit.allowance,
          frequency: benefit.frequency,
          amount: benefit.amount,
        })),
        acceptanceDeadline: formattedDeadline,
        hrManagerName: `${currentUser.firstName} ${currentUser.lastName}`,
        hrEmail: currentUser.email,
        hrPhone: (currentUser as any).phone || '',
        currentDate: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
      };

      // Generate and upload offer letter PDF
      const pdfUploadResult = await this.generatePdfFromTemplateAndUpload(
        'offer-letter',
        token || '',
        currentUserId,
        offerData,
      );

      // Update interview details with offer letter PDF URL
      if (pdfUploadResult?.uploadResult?.data?.fullUrl) {
        await this.crudHelperService.update(
          this.sequelize.models['InterviewDetails'],
          {
            offerLetterPdfUrl: pdfUploadResult.uploadResult.data.fullUrl,
            offerLetterPdfFileName:
              pdfUploadResult.uploadResult.data.originalName,
          },
          { where: { candidateId } },
          transaction,
        );
      }

      // Update candidate status or create interview details record for tracking
      const interviewDetails = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        {
          where: { candidateId },
        },
      );

      console.log(interviewDetails);

      if (interviewDetails) {
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          { status: CandidateStatus.OFFER_SENT },
          { where: { id: candidateId } },
          transaction,
        );
      }

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        success: true,
        message: 'Offer letter saved successfully',
      };
    } catch (error) {
      console.log(error);
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async createInterviewDetails(
    createInterviewDetailsDto: CreateInterviewDetailsDto,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      const candidate = await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: { id: createInterviewDetailsDto.candidateId },
        },
      );
      console.log(candidate);
      if (!candidate) {
        throw new HttpException('Candidate not found', 404);
      }
      const interviewer = await this.crudHelperService.findOne(
        this.sequelize.models['User'],
        {
          where: { id: createInterviewDetailsDto.interviewerId },
        },
      );
      console.log(candidate);
      if (!interviewer) {
        throw new HttpException('Interviewer not found', 404);
      }
      const createdInterviewDetails = await this.crudHelperService.create(
        this.sequelize.models['InterviewDetails'],
        {
          candidateId: createInterviewDetailsDto.candidateId,
          interviewerId: createInterviewDetailsDto.interviewerId,
          interviewDate: createInterviewDetailsDto.interviewDate,
          interviewTime: createInterviewDetailsDto.interviewTime || '',
          interviewType: createInterviewDetailsDto.interviewType || '',
          interviewMode: createInterviewDetailsDto.interviewMode || '',
          location: createInterviewDetailsDto.location || '',
          meetingLink: createInterviewDetailsDto.meetingLink || '',
          customMessage: createInterviewDetailsDto.customMessage || '',
        },
        transaction,
      );

      // Update candidate status to PENDING when interview details are created
      await this.crudHelperService.update(
        this.sequelize.models['Candidate'],
        { status: CandidateStatus.INVITATION_PENDING },
        { where: { id: createInterviewDetailsDto.candidateId } },
        transaction,
      );

      // Update candidate flow step based on current state
      await this.updateCandidateFlowStep(
        createInterviewDetailsDto.candidateId,
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return createdInterviewDetails;
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }
  async getCandidateById(candidateId: number): Promise<any> {
    return this.crudHelperService.findOne(this.sequelize.models['Candidate'], {
      where: { id: candidateId },
      include: [
        {
          model: this.sequelize.models['InterviewDetails'],
          required: false,
          include: [
            {
              model: this.userModel,
              as: 'interviewer',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
        {
          model: this.sequelize.models['Position'],
          attributes: ['id', 'title'],
        },
        {
          model: this.sequelize.models['Department'],
          attributes: ['id', 'name'],
        },
        {
          model: this.sequelize.models['Compensation'],
          required: false,
          include: [
            {
              model: this.sequelize.models['Benefit'],
              attributes: ['id', 'allowance', 'frequency', 'amount'],
            },
          ],
        },
      ],
    });
  }

  async getInterviewers(currentUserId: number): Promise<Partial<User>[]> {
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    // Get all users from the same company who can be interviewers
    return this.crudHelperService.findAll<User>(this.userModel, {
      where: { companyId: currentUser.companyId },
      attributes: ['id', 'firstName', 'lastName', 'email', 'username'],
      include: [
        {
          model: UserRole,
          include: [
            {
              model: Role,
            },
          ],
        },
      ],
    });
  }

  async getInterviewDetailsByStatus(
    currentUserId: number,
    interviewDetailsId?: number,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResult<any>> {
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    const candidateWhereClause: any = { companyId: currentUser.companyId };
    if (status) {
      candidateWhereClause.status = status;
    }

    return this.crudHelperService.paginateWithQuery(
      this.sequelize.models['InterviewDetails'],
      {
        page,
        limit,
        include: [
          {
            model: this.sequelize.models['Candidate'],
            where: candidateWhereClause,
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'positionId',
              'status',
            ],
            include: [
              {
                model: this.sequelize.models['Position'],
                attributes: ['id', 'title'],
              },
            ],
          },
          {
            model: this.userModel,
            as: 'interviewer',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
        where: {
          id: interviewDetailsId,
        },
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async updateCandidateStatus(
    candidateId: number,
    status: CandidateStatus,
    currentUserId: number,
  ): Promise<{ success: boolean; message: string }> {
    // const shouldCommit = !transaction;
    // if (shouldCommit) {
    //   transaction = await this.sequelize.transaction();
    // }

    try {
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });

      if (!currentUser?.companyId) {
        throw new ConflictException('User is not associated with any company');
      }
      // Update the candidate status instead of interview invitation status
      await this.crudHelperService.update(
        this.sequelize.models['Candidate'],
        { status: status },
        { where: { id: candidateId } },
        // transaction,
      );

      // if (shouldCommit) {
      //   await transaction.commit();
      // }

      return {
        success: true,
        message: `Candidate status updated to ${status}`,
      };
    } catch (error) {
      console.log(error);
      // if (shouldCommit) {
      //   await transaction.rollback();
      // }
      throw error;
    }
  }

  async getCandidatesWithInterviewDetails(
    currentUserId: number,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: CandidateStatus,
    department?: string,
  ): Promise<PaginatedResult<any>> {
    const currentUser = await this.crudHelperService.findOne<User>(
      this.userModel,
      {
        where: { id: currentUserId },
      },
    );

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    const candidateWhere: any = { companyId: currentUser.companyId };

    if (search) {
      candidateWhere[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { '$position.title$': { [Op.iLike]: `%${search}%` } },
        { '$department.name$': { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (department) {
      candidateWhere.departmentId = department;
    }

    if (status) {
      candidateWhere.status = status;
    }

    return this.crudHelperService.paginateWithQuery(
      this.sequelize.models['Candidate'],
      {
        page,
        limit,
        where: candidateWhere,
        include: [
          {
            model: this.sequelize.models['InterviewDetails'],
            as: 'interview',
            required: false,
            include: [
              {
                model: this.userModel,
                as: 'interviewer',
                attributes: ['id', 'firstName', 'lastName', 'email'],
              },
            ],
          },
          {
            model: this.sequelize.models['Department'],
            as: 'department',
            required: false,
            attributes: ['id', 'name'],
          },
          {
            model: this.sequelize.models['Position'],
            as: 'position',
            required: false,
            attributes: ['id', 'title'],
          },
        ],
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'phone',
          'positionId',
          'departmentId',
          'status',
          'flowStep',
          'resumeURL',
          'passportNumber',
          'passportURL',
          'createdAt',
          'updatedAt',
        ],
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async getPositions(currentUserId: number) {
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    return await this.crudHelperService.findAll(
      this.sequelize.models['Position'],
      {
        where: {
          companyId: currentUser.companyId,
          isActive: true,
        },
        attributes: ['id', 'title', 'description'],
        order: [['title', 'ASC']],
      },
    );
  }

  /**
   * Determines and updates the candidate's flow step based on their current state
   */
  async updateCandidateFlowStep(
    candidateId: number,
    transaction?: any,
  ): Promise<CandidateFlowStep> {
    try {
      // Get candidate with related data
      const candidate = await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: { id: candidateId },
          include: [
            {
              model: this.sequelize.models['InterviewDetails'],
              as: 'interview',
              required: false,
            },
            {
              model: this.sequelize.models['Compensation'],
              required: false,
            },
          ],
        },
        transaction,
      );

      if (!candidate) {
        throw new NotFoundException('Candidate not found');
      }

      let newFlowStep: CandidateFlowStep;

      // Determine the flow step based on candidate state
      const hasInterviewDetails = !!(candidate as any).interview;
      const hasCompensation = !!(candidate as any).compensation;

      if (!hasInterviewDetails) {
        // No interview details yet - candidate is at invitation step
        newFlowStep = CandidateFlowStep.INVITATION;
      } else if (hasInterviewDetails && !hasCompensation) {
        // Has interview details but no compensation - next step is compensation
        newFlowStep = CandidateFlowStep.COMPENSATION;
      } else if (hasInterviewDetails && hasCompensation) {
        // Has both interview and compensation - ready for offer
        newFlowStep = CandidateFlowStep.SHARED_OFFER;
      } else {
        // Default to invitation step
        newFlowStep = CandidateFlowStep.INVITATION;
      }

      // Update the candidate's flow step if it's different
      if ((candidate as any).flowStep !== newFlowStep) {
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          { flowStep: newFlowStep },
          { where: { id: candidateId } },
          transaction,
        );
      }

      return newFlowStep;
    } catch (error) {
      console.error('Error updating candidate flow step:', error);
      throw error;
    }
  }

  /**
   * Gets the candidate's current flow step
   */
  async getCandidateFlowStep(candidateId: number): Promise<CandidateFlowStep> {
    const candidate = await this.crudHelperService.findOne(
      this.sequelize.models['Candidate'],
      {
        where: { id: candidateId },
        attributes: ['id', 'flowStep'],
      },
    );

    if (!candidate) {
      throw new NotFoundException('Candidate not found');
    }

    return (candidate as any).flowStep;
  }
}

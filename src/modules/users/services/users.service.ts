import {
  ConflictException,
  HttpException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { Op } from 'sequelize';
import { User } from 'src/models/users-models/user.model';
import { CreateUserDto, UpdateUserDto } from '../dto';
import { CreateCandidateDto } from '../dto/create-candidate.dto';
import { CreateInterviewDetailsDto } from '../dto/create-interview-details.dto';
import * as bcrypt from 'bcrypt';
import { UserRoleService } from 'src/modules/roles-and-permissions/user-role/user-role.service';
import { RedirectSectionEnum } from 'src/utils/redirect-section.enum';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { ROLES_ENUM } from 'src/modules/roles-and-permissions/roles/utils/enums';
import { Sequelize } from 'sequelize-typescript';
import { ConfigService } from '@nestjs/config';
import { AppConfig } from 'src/config/config.interface';
import { FilesService } from 'src/modules/files/srevices/files.service';
import { EmailService } from 'src/modules/email/email.service';
import { SendInterviewInvitationDto } from '../dto/send-interview-invitation.dto';
import { SendInvitationOnlyDto } from '../dto/send-invitation-only.dto';
import { Candidate } from 'src/models/users-models/candidate.model';
import { InvitationStatus } from '../enums/invitation-status.enum';
import { CandidateFlowStep } from '../enums/candidate-flow-step.enum';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(EmploymentDetails)
    private employeeDetailsModel: typeof EmploymentDetails,
    private readonly crudHelperService: CrudHelperService,
    private readonly userRoleService: UserRoleService,
    private readonly sequelize: Sequelize,
    private readonly configService: ConfigService<AppConfig>,
    private readonly filesService: FilesService,
    private readonly emailService: EmailService,
  ) {}

  //platform
  async findAll(): Promise<Partial<User>[]> {
    return this.crudHelperService.findAll<User>(this.userModel);
  }

  async findOne(id: number): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { id },
    });
  }

  async findOneWithRefreshToken(id: number): Promise<Partial<User>> {
    return this.userModel.scope('withRefreshToken').findOne({
      where: { id },
    });
  }

  async findByUsername(username: string): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { username },
    });
  }

  async findByEmail(email: string): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { email },
    });
  }

  async findByEmailWithPassword(email: string): Promise<Partial<User>> {
    return this.userModel.scope('withPassword').findOne({
      where: { email },
    });
  }

  async findByGoogleId(googleId: string): Promise<Partial<User>> {
    return this.crudHelperService.findOne<User>(this.userModel, {
      where: { googleId },
    });
  }

  async remove(id: number): Promise<void> {
    await this.crudHelperService.delete(this.userModel, {
      where: { id },
    });
  }

  async uploadProfileImage(
    file: any,
    token: string,
    userId: number,
  ): Promise<{ uploadResult: any }> {
    return this.filesService.uploadFile(file, token, userId);
  }

  async createUser(
    createUserDto: CreateUserDto,
    currentUserId?: number,
  ): Promise<Partial<User>> {
    return this.createUserWithTransaction(createUserDto, currentUserId);
  }

  async createUserWithTransaction(
    createUserDto: CreateUserDto,
    currentUserId?: number,
    transaction?: any,
  ): Promise<Partial<User>> {
    // If no transaction provided, create one
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Check for existing username and email (outside transaction for better performance)
      const [existingUsername, existingEmail] = await Promise.all([
        this.userModel.findOne({ where: { username: createUserDto.username } }),
        this.userModel.findOne({ where: { email: createUserDto.email } }),
      ]);

      if (existingUsername) {
        throw new ConflictException('Username already exists');
      }

      if (existingEmail) {
        throw new ConflictException('Email already exists');
      }

      if (!createUserDto.roleId) {
        throw new ConflictException('Role ID is required for user creation');
      }

      const defaultPassword =
        this.configService.get<AppConfig['user']>('user').defaultPassword;
      const rawPassword = createUserDto.password || defaultPassword;
      const hashedPassword = await this.hashPassword(rawPassword);

      const createdUser = await this.userModel.create(
        {
          email: createUserDto.email,
          username: createUserDto.username,
          password: hashedPassword,
          firstName: createUserDto.firstName,
          lastName: createUserDto.lastName,
          redirectTo:
            createUserDto.redirectTo ?? RedirectSectionEnum.CREATE_COMPANY,
        },
        { transaction },
      );

      await this.userRoleService.assignRoleWithTransaction(
        createdUser.id,
        createUserDto.roleId,
        transaction,
      );

      const hasEmploymentFields =
        createUserDto.position &&
        createUserDto.departmentId &&
        createUserDto.hireDate;

      if (currentUserId && hasEmploymentFields) {
        const currentUserData = await this.userModel.findOne({
          where: { id: currentUserId },
        });

        if (!currentUserData?.companyId) {
          throw new ConflictException('Company context is missing');
        }

        // Set companyId for the new user
        await createdUser.update(
          { companyId: currentUserData.companyId },
          { transaction },
        );

        await this.employeeDetailsModel.create(
          {
            userId: createdUser.id,
            position: createUserDto.position,
            departmentId: createUserDto.departmentId,
            hireDate: createUserDto.hireDate,
          },
          { transaction },
        );
      }

      // Commit transaction if we created it
      if (shouldCommit) {
        await transaction.commit();
      }

      return createdUser;
    } catch (error) {
      // Rollback transaction if we created it
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async updateUser(
    id: number,
    updateUserDto: UpdateUserDto,
    transaction?: any,
  ): Promise<Partial<User>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    // Use transaction-aware find operation
    await this.crudHelperService.findOne<User>(
      this.userModel,
      { where: { id } },
      transaction,
    );

    if (updateUserDto.password) {
      updateUserDto.password = await this.hashPassword(updateUserDto.password);
    }

    if (updateUserDto.username) {
      const existingUsername = await this.userModel.findOne({
        where: { username: updateUserDto.username },
        transaction,
      });
      if (existingUsername && existingUsername.id !== id) {
        throw new ConflictException('Username already exists');
      }
    }

    if (updateUserDto.email) {
      const existingEmail = await this.userModel.findOne({
        where: { email: updateUserDto.email },
        transaction,
      });
      if (existingEmail && existingEmail.id !== id) {
        throw new ConflictException('Email already exists');
      }
    }

    const updatedUser = await this.crudHelperService.update<User>(
      this.userModel,
      updateUserDto,
      {
        where: { id },
      },
      transaction,
    );

    if (shouldCommit) {
      await transaction.commit();
    }

    return updatedUser;
  }

  async getAllUsersPaginated(
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<User>>> {
    const result = await this.crudHelperService.paginateWithQuery<User>(
      this.userModel,
      {
        page,
        limit,
        attributes: [
          'id',
          'username',
          'email',
          'firstName',
          'lastName',
          'status',
          'profileImage',
        ],
        include: [
          {
            model: UserRole,
            include: [
              {
                model: Role,
                where: {
                  name: [
                    ROLES_ENUM.COMPANY_ADMIN,
                    ROLES_ENUM.COMPANY_SUPER_ADMIN,
                  ],
                },
              },
            ],
          },
          {
            model: EmploymentDetails,
            required: false,
            limit: 1,
          },
        ],
      },
    );

    // Transform employmentDetails from array to single object
    const transformedData = result.data.map((user) => {
      const userData = user.toJSON ? user.toJSON() : user;
      if (
        userData.employmentDetails &&
        Array.isArray(userData.employmentDetails)
      ) {
        userData.employmentDetails =
          userData.employmentDetails.length > 0
            ? userData.employmentDetails[0]
            : null;
      }
      return userData;
    });

    return {
      ...result,
      data: transformedData,
    };
  }

  async getUsersByCompanyPaginated(
    userId: number,
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<User>>> {
    const currentUser = await this.crudHelperService.findOne<User>(
      this.userModel,
      { where: { id: userId } },
    );

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    const result = await this.crudHelperService.paginateWithQuery<User>(
      this.userModel,
      {
        page,
        limit,
        attributes: [
          'id',
          'username',
          'email',
          'firstName',
          'lastName',
          'status',
          'profileImage',
        ],
        where: { companyId: currentUser.companyId },
        include: [
          {
            model: EmploymentDetails,
            required: false,
          },
          {
            model: UserRole,
            include: [
              {
                model: Role,
              },
            ],
          },
        ],
      },
    );

    // Transform employmentDetails from array to single object
    const transformedData = result.data.map((user) => {
      const userData = user.toJSON ? user.toJSON() : user;
      if (
        userData.employmentDetails &&
        Array.isArray(userData.employmentDetails)
      ) {
        userData.employmentDetails =
          userData.employmentDetails.length > 0
            ? userData.employmentDetails[0]
            : null;
      }
      return userData;
    });

    return {
      ...result,
      data: transformedData,
    };
  }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  async validatePassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async isAdmin(userId: number): Promise<boolean> {
    const admin = await this.userRoleService.findUserById(userId);
    return !!admin;
  }

  async storeUserRefreshToken(
    userId: number,
    refreshToken: string,
    transaction?: any,
  ): Promise<void> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    // Use transaction-aware find operation
    const user = await this.crudHelperService.findOne<User>(
      this.userModel,
      { where: { id: userId } },
      transaction,
    );

    if (!user) {
      throw new ConflictException('User not found');
    }

    await this.updateUser(
      user.id,
      {
        refreshToken: refreshToken,
      },
      transaction,
    );

    if (shouldCommit) {
      await transaction.commit();
    }
  }

  async generateEmployeeId(): Promise<string> {
    const lastEmployee = await this.userModel.findOne({
      order: [['createdAt', 'DESC']],
    });

    const lastId = lastEmployee ? parseInt(lastEmployee.id.toString()) : 0;
    const newId = lastId + 1;
    const employeeId = `EMP${newId.toString().padStart(3, '0')}`;
    return employeeId;
  }

  // Candidate functions

  async createCandidateWithTransaction(
    createCandidateDto: CreateCandidateDto,
    currentUserId: number,
    files?: { resume?: any; passport?: any }, // Object containing both file types
    token?: string,
    transaction?: any,
  ): Promise<Partial<any>> {
    // If no transaction provided, create one
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Check for existing email using crudHelperService
      const existingEmail = await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: { email: createCandidateDto.email },
        },
      );
      if (existingEmail) {
        throw new ConflictException('Email already exists');
      }
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });
      const companyId = currentUser?.companyId;
      if (!companyId) {
        throw new ConflictException(
          'Company ID is required for candidate creation',
        );
      }

      if (!companyId) {
        throw new ConflictException(
          'Company ID is required for candidate creation',
        );
      }

      console.log(createCandidateDto);

      // First, create the candidate without file URLs
      const createdCandidate = await this.crudHelperService.create(
        this.sequelize.models['Candidate'],
        {
          email: createCandidateDto.email,
          firstName: createCandidateDto.firstName,
          middleName: createCandidateDto.middleName || '',
          lastName: createCandidateDto.lastName,
          gender: createCandidateDto.gender || '',
          positionId: createCandidateDto.positionId || null,
          departmentId: createCandidateDto.departmentId || 0,
          phone: createCandidateDto.phone || '',
          passportNumber: createCandidateDto.passportNumber || '',
          passportExpiryDate: createCandidateDto.passportExpiryDate || '',
          companyId: companyId,
          countryId: createCandidateDto.countryId || 0,
          resumeURL: '', // Initially empty, will be updated after file upload
          passportURL: '', // Initially empty, will be updated after file upload
        },
        transaction,
      );

      // Now upload the files using the candidate's ID
      let resumeURL = '';
      let passportURL = '';
      const candidateId = (createdCandidate as any).id;

      // Upload resume file if provided
      if (files?.resume && token && candidateId) {
        const resumeUploadResult = await this.filesService.uploadFile(
          files.resume,
          token,
          candidateId, // Use the created candidate's ID
        );
        resumeURL = resumeUploadResult.uploadResult.data.fullUrl || '';
        console.log(resumeUploadResult, 'passportURL');
      }

      // Upload passport file if provided
      if (files?.passport && token && candidateId) {
        const passportUploadResult = await this.filesService.uploadFile(
          files.passport,
          token,
          candidateId, // Use the created candidate's ID
        );
        passportURL = passportUploadResult.uploadResult.data.fullUrl || '';
      }
      console.log(resumeURL, 'resumeURL');
      // Update the candidate with the file URLs if any were uploaded
      if (resumeURL || passportURL) {
        const updateData: any = {};
        if (resumeURL) updateData.resumeURL = resumeURL;
        if (passportURL) updateData.passportURL = passportURL;

        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          updateData,
          { where: { id: candidateId } },
          transaction,
        );

        // Update the local object to reflect the changes
        if (resumeURL) (createdCandidate as any).resumeURL = resumeURL;
        if (passportURL) (createdCandidate as any).passportURL = passportURL;
      }

      // Commit transaction if we created it
      if (shouldCommit) {
        await transaction.commit();
      }

      return createdCandidate;
    } catch (error) {
      // Rollback transaction if we created it
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async removeCandidate(id: number): Promise<void> {
    await this.crudHelperService.delete(this.sequelize.models['Candidate'], {
      where: { id },
    });
  }

  async sendInterviewInvitationAndSave(
    invitationData: SendInterviewInvitationDto,
    currentUserId: number,
    transaction?: any,
  ): Promise<{ success: boolean; message: string; interviewDetails?: any }> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company information
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
        include: [
          {
            model: this.sequelize.models['Company'],
            as: 'company',
          },
        ],
      });

      const candidate = (await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: { id: invitationData.candidateId },
          include: [
            {
              model: this.sequelize.models['Position'],
              attributes: ['id', 'title'],
            },
          ],
        },
      )) as Candidate;

      if (!candidate) {
        throw new NotFoundException('Candidate not found');
      }

      const interviwer = (await this.userModel.findOne({
        where: { id: invitationData.interviewerId },
      })) as User;
      if (!interviwer) {
        throw new NotFoundException('Interviewer not found');
      }

      if (!interviwer) {
        throw new NotFoundException('Interviewer not found');
      }

      // Check if interview details already exist for this candidate
      const existingInterviewDetails = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        {
          where: { candidateId: (candidate as any).id },
        },
      );

      const interviewData = {
        candidateId: (candidate as any).id,
        interviewerId: invitationData.interviewerId,
        interviewDate: new Date(invitationData.interviewDate),
        interviewTime: invitationData.interviewTime,
        interviewType: invitationData.interviewType,
        interviewMode: invitationData.interviewMode,
        location: invitationData.location,
        meetingLink: invitationData.meetingLink,
        customMessage: invitationData.customMessage || '',
      };

      if (existingInterviewDetails) {
        // Update existing interview details
        await this.crudHelperService.update(
          this.sequelize.models['InterviewDetails'],
          interviewData,
          { where: { candidateId: (candidate as any).id } },
          transaction,
        );
      } else {
        // Create new interview details record
        await this.crudHelperService.create(
          this.sequelize.models['InterviewDetails'],
          interviewData,
          transaction,
        );
      }

      const company = (currentUser as any).company;

      // Send interview invitation email only if isToBeSent is true
      if (invitationData.isToBeSent) {
        await this.emailService.sendInterviewInvitation({
          candidateEmail: candidate.email,
          candidateFirstName: candidate.firstName,
          candidateLastName: candidate.lastName,
          interviewDate: invitationData.interviewDate,
          interviewTime: invitationData.interviewTime,
          interviewType: invitationData.interviewType,
          interviewMode: invitationData.interviewMode,
          location: invitationData.location,
          meetingLink: invitationData.meetingLink,
          interviewerName: interviwer.firstName + ' ' + interviwer.lastName,
          customMessage: invitationData.customMessage,
          position: candidate.position?.title || 'Not specified',
          companyName: company.name || 'Softbuilders default name',
        });

        // Update candidate status to SENT only after email is successfully sent
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          { status: InvitationStatus.OFFER_SENT },
          { where: { id: (candidate as any).id } },
          transaction,
        );
      }

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        success: true,
        message: invitationData.isToBeSent
          ? 'Interview invitation sent successfully'
          : 'Interview details saved successfully',
        interviewDetails: existingInterviewDetails ? 'updated' : 'created',
      };
    } catch (error) {
      console.log('the error comes from here', error);
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async sendInvitationOnly(
    sendInvitationDto: SendInvitationOnlyDto,
    currentUserId: number,
    transaction?: any,
  ): Promise<{ success: boolean; message: string }> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company information
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
        include: [
          {
            model: this.sequelize.models['Company'],
            as: 'company',
          },
        ],
      });

      if (!currentUser?.companyId) {
        throw new ConflictException('User is not associated with any company');
      }

      // Get the interview details with candidate and interviewer information
      const interviewDetails = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        {
          where: { id: sendInvitationDto.interviewDetailsId },
          include: [
            {
              model: this.sequelize.models['Candidate'],
              where: { companyId: currentUser.companyId }, // Ensure candidate belongs to same company
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
            {
              model: this.userModel,
              as: 'interviewer',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
      );

      console.log(interviewDetails);

      if (!interviewDetails) {
        throw new NotFoundException(
          'Interview details not found or not authorized',
        );
      }

      const candidate = (interviewDetails as any).Candidate;
      const interviewer = (interviewDetails as any).interviewer;
      const company = (currentUser as any).company || { name: 'Company' };

      // Use custom message from DTO if provided, otherwise use the one from interview details
      const customMessage =
        sendInvitationDto.customMessage ||
        (interviewDetails as any).customMessage;

      // Send interview invitation email
      await this.emailService.sendInterviewInvitation({
        candidateEmail: candidate.email,
        candidateFirstName: candidate.firstName,
        candidateLastName: candidate.lastName,
        interviewDate: (interviewDetails as any).interviewDate,
        interviewTime: (interviewDetails as any).interviewTime,
        interviewType: (interviewDetails as any).interviewType,
        interviewMode: (interviewDetails as any).interviewMode,
        location: (interviewDetails as any).location,
        meetingLink: (interviewDetails as any).meetingLink,
        interviewerName: `${interviewer.firstName} ${interviewer.lastName}`,
        customMessage: customMessage,
        position: candidate.position || 'Position',
        companyName: company.name,
        companyEmail: company.email,
      });

      // Update the candidate status to SENT and interview details custom message
      const interviewDetailsForUpdate = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        { where: { id: sendInvitationDto.interviewDetailsId } },
      );

      if (interviewDetailsForUpdate) {
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          { status: InvitationStatus.OFFER_SENT },
          { where: { id: (interviewDetailsForUpdate as any).candidateId } },
          transaction,
        );

        // Update custom message if provided
        if (customMessage) {
          await this.crudHelperService.update(
            this.sequelize.models['InterviewDetails'],
            { customMessage: customMessage },
            { where: { id: sendInvitationDto.interviewDetailsId } },
            transaction,
          );
        }
      }

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        success: true,
        message: 'Interview invitation sent successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async sendOfferLetter(
    candidateId: number,
    currentUserId: number,
    transaction?: any,
  ): Promise<{ success: boolean; message: string }> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      // Get current user's company information
      const currentUser = await this.crudHelperService.findOne<User>(
        this.userModel,
        {
          where: { id: currentUserId },
          include: [
            {
              model: this.sequelize.models['Company'],
              as: 'company',
            },
          ],
        },
      );

      if (!currentUser?.companyId) {
        throw new ConflictException('User is not associated with any company');
      }

      // Get candidate with compensation and benefits
      const candidate = await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: {
            id: candidateId,
            companyId: currentUser.companyId,
          },
          include: [
            {
              model: this.sequelize.models['Compensation'],
              as: 'compensation',
              include: [
                {
                  model: this.sequelize.models['Benefit'],
                  as: 'benefits',
                },
              ],
            },
            {
              model: this.sequelize.models['Country'],
              as: 'country',
              attributes: ['name'],
            },
          ],
        },
      );

      if (!candidate) {
        throw new NotFoundException('Candidate not found or not authorized');
      }

      const compensation = (candidate as any).compensation;
      if (!compensation) {
        throw new NotFoundException(
          'Compensation details not found for candidate',
        );
      }

      const company = (currentUser as any).company || {
        name: 'Company',
        email: '<EMAIL>',
        phone: '',
        website: '',
        address: '',
      };

      // Generate automatic offer details
      const startDate = new Date();
      startDate.setDate(startDate.getDate() + 30); // 30 days from now
      const formattedStartDate = startDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      const acceptanceDeadline = new Date();
      acceptanceDeadline.setDate(acceptanceDeadline.getDate() + 7); // 7 days from now
      const formattedDeadline = acceptanceDeadline.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      // Prepare offer letter data
      const offerData = {
        candidateEmail: (candidate as any).email,
        candidateFirstName: (candidate as any).firstName,
        candidateLastName: (candidate as any).lastName,
        position: (candidate as any).position || 'Position',
        companyName: company.name,
        companyEmail: company.email,
        companyPhone: company.phone,
        companyWebsite: company.website,
        companyAddress: company.address,
        startDate: formattedStartDate,
        employmentType: 'Full-time',
        location: company.address || 'Office Location',
        reportingTo: 'Department Manager',
        currency: compensation.currency || 'AED',
        totalMonthlySalary: compensation.totalSalary || 0,
        basicSalary: compensation.basicSalary || 0,
        housing: compensation.housing || 0,
        transportation: compensation.transportation || 0,
        others: compensation.others || 0,
        benefits: (compensation.benefits || []).map((benefit: any) => ({
          allowance: benefit.allowance,
          frequency: benefit.frequency,
          amount: benefit.amount,
        })),
        acceptanceDeadline: formattedDeadline,
        hrManagerName: `${currentUser.firstName} ${currentUser.lastName}`,
        hrEmail: currentUser.email,
        hrPhone: (currentUser as any).phone || '',
      };

      // Send offer letter email
      await this.emailService.sendOfferLetter(offerData);

      // Update candidate status or create interview details record for tracking
      const interviewDetails = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        {
          where: { candidateId },
        },
      );

      if (interviewDetails) {
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          { status: InvitationStatus.OFFER_SENT },
          { where: { id: candidateId } },
          transaction,
        );
      }

      if (shouldCommit) {
        await transaction.commit();
      }

      return {
        success: true,
        message: 'Offer letter sent successfully',
      };
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async createInterviewDetails(
    createInterviewDetailsDto: CreateInterviewDetailsDto,
    transaction?: any,
  ): Promise<Partial<any>> {
    const shouldCommit = !transaction;
    if (shouldCommit) {
      transaction = await this.sequelize.transaction();
    }

    try {
      const candidate = await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: { id: createInterviewDetailsDto.candidateId },
        },
      );
      console.log(candidate);
      if (!candidate) {
        throw new HttpException('Candidate not found', 404);
      }
      const interviewer = await this.crudHelperService.findOne(
        this.sequelize.models['User'],
        {
          where: { id: createInterviewDetailsDto.interviewerId },
        },
      );
      console.log(candidate);
      if (!interviewer) {
        throw new HttpException('Interviewer not found', 404);
      }
      const createdInterviewDetails = await this.crudHelperService.create(
        this.sequelize.models['InterviewDetails'],
        {
          candidateId: createInterviewDetailsDto.candidateId,
          interviewerId: createInterviewDetailsDto.interviewerId,
          interviewDate: createInterviewDetailsDto.interviewDate,
          interviewTime: createInterviewDetailsDto.interviewTime || '',
          interviewType: createInterviewDetailsDto.interviewType || '',
          interviewMode: createInterviewDetailsDto.interviewMode || '',
          location: createInterviewDetailsDto.location || '',
          meetingLink: createInterviewDetailsDto.meetingLink || '',
          customMessage: createInterviewDetailsDto.customMessage || '',
        },
        transaction,
      );

      // Update candidate status to PENDING when interview details are created
      await this.crudHelperService.update(
        this.sequelize.models['Candidate'],
        { status: InvitationStatus.INVITATION_PENDING },
        { where: { id: createInterviewDetailsDto.candidateId } },
        transaction,
      );

      // Update candidate flow step based on current state
      await this.updateCandidateFlowStep(
        createInterviewDetailsDto.candidateId,
        transaction,
      );

      if (shouldCommit) {
        await transaction.commit();
      }

      return createdInterviewDetails;
    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      throw error;
    }
  }
  async getCandidateById(candidateId: number): Promise<any> {
    return this.crudHelperService.findOne(this.sequelize.models['Candidate'], {
      where: { id: candidateId },
      include: [
        {
          model: this.sequelize.models['InterviewDetails'],
          required: false,
          include: [
            {
              model: this.userModel,
              as: 'interviewer',
              attributes: ['id', 'firstName', 'lastName', 'email'],
            },
          ],
        },
        {
          model: this.sequelize.models['Position'],
          attributes: ['id', 'title'],
        },
        {
          model: this.sequelize.models['Department'],
          attributes: ['id', 'name'],
        },
        {
          model: this.sequelize.models['Compensation'],
          required: false,
          include: [
            {
              model: this.sequelize.models['Benefit'],
              attributes: ['id', 'allowance', 'frequency', 'amount'],
            },
          ],
        },
      ],
    });
  }

  async getInterviewers(currentUserId: number): Promise<Partial<User>[]> {
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    // Get all users from the same company who can be interviewers
    return this.crudHelperService.findAll<User>(this.userModel, {
      where: { companyId: currentUser.companyId },
      attributes: ['id', 'firstName', 'lastName', 'email', 'username'],
      include: [
        {
          model: UserRole,
          include: [
            {
              model: Role,
            },
          ],
        },
      ],
    });
  }

  async getInterviewDetailsByStatus(
    currentUserId: number,
    status?: InvitationStatus,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResult<any>> {
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    const candidateWhereClause: any = { companyId: currentUser.companyId };
    if (status) {
      candidateWhereClause.status = status;
    }

    return this.crudHelperService.paginateWithQuery(
      this.sequelize.models['InterviewDetails'],
      {
        page,
        limit,
        include: [
          {
            model: this.sequelize.models['Candidate'],
            where: candidateWhereClause,
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'positionId',
              'status',
            ],
            include: [
              {
                model: this.sequelize.models['Position'],
                attributes: ['id', 'title'],
              },
            ],
          },
          {
            model: this.userModel,
            as: 'interviewer',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async updateInvitationStatus(
    interviewDetailsId: number,
    status: InvitationStatus,
    currentUserId: number,
  ): Promise<{ success: boolean; message: string }> {
    // const shouldCommit = !transaction;
    // if (shouldCommit) {
    //   transaction = await this.sequelize.transaction();
    // }

    try {
      const currentUser = await this.userModel.findOne({
        where: { id: currentUserId },
      });

      if (!currentUser?.companyId) {
        throw new ConflictException('User is not associated with any company');
      }

      // Verify the interview details belongs to the user's company
      const interviewDetails = await this.crudHelperService.findOne(
        this.sequelize.models['InterviewDetails'],
        {
          where: { id: interviewDetailsId },
          include: [
            {
              model: this.sequelize.models['Candidate'],
              where: { companyId: currentUser.companyId },
            },
          ],
        },
      );

      if (!interviewDetails) {
        throw new NotFoundException(
          'Interview details not found or not authorized',
        );
      }

      // Update the candidate status instead of interview invitation status
      await this.crudHelperService.update(
        this.sequelize.models['Candidate'],
        { status: status },
        { where: { id: (interviewDetails as any).candidateId } },
        // transaction,
      );

      // if (shouldCommit) {
      //   await transaction.commit();
      // }

      return {
        success: true,
        message: `Invitation status updated to ${status}`,
      };
    } catch (error) {
      console.log(error);
      // if (shouldCommit) {
      //   await transaction.rollback();
      // }
      throw error;
    }
  }

  async getCandidatesWithInterviewDetails(
    currentUserId: number,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: InvitationStatus,
    department?: string,
  ): Promise<PaginatedResult<any>> {
    const currentUser = await this.crudHelperService.findOne<User>(
      this.userModel,
      {
        where: { id: currentUserId },
      },
    );

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    const candidateWhere: any = { companyId: currentUser.companyId };

    if (search) {
      candidateWhere[Op.or] = [
        { firstName: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { '$position.title$': { [Op.iLike]: `%${search}%` } },
        { '$department.name$': { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (department) {
      candidateWhere.departmentId = department;
    }

    if (status) {
      candidateWhere.status = status;
    }

    return this.crudHelperService.paginateWithQuery(
      this.sequelize.models['Candidate'],
      {
        page,
        limit,
        where: candidateWhere,
        include: [
          {
            model: this.sequelize.models['InterviewDetails'],
            as: 'interview',
            required: false,
            include: [
              {
                model: this.userModel,
                as: 'interviewer',
                attributes: ['id', 'firstName', 'lastName', 'email'],
              },
            ],
          },
          {
            model: this.sequelize.models['Department'],
            as: 'department',
            required: false,
            attributes: ['id', 'name'],
          },
          {
            model: this.sequelize.models['Position'],
            as: 'position',
            required: false,
            attributes: ['id', 'title'],
          },
        ],
        attributes: [
          'id',
          'firstName',
          'lastName',
          'email',
          'phone',
          'positionId',
          'departmentId',
          'status',
          'flowStep',
          'resumeURL',
          'passportNumber',
          'passportURL',
          'createdAt',
          'updatedAt',
        ],
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async getPositions(currentUserId: number) {
    const currentUser = await this.userModel.findOne({
      where: { id: currentUserId },
    });

    if (!currentUser?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    return await this.crudHelperService.findAll(
      this.sequelize.models['Position'],
      {
        where: {
          companyId: currentUser.companyId,
          isActive: true,
        },
        attributes: ['id', 'title', 'description'],
        order: [['title', 'ASC']],
      },
    );
  }

  /**
   * Determines and updates the candidate's flow step based on their current state
   */
  async updateCandidateFlowStep(
    candidateId: number,
    transaction?: any,
  ): Promise<CandidateFlowStep> {
    try {
      // Get candidate with related data
      const candidate = await this.crudHelperService.findOne(
        this.sequelize.models['Candidate'],
        {
          where: { id: candidateId },
          include: [
            {
              model: this.sequelize.models['InterviewDetails'],
              as: 'interview',
              required: false,
            },
            {
              model: this.sequelize.models['Compensation'],
              required: false,
            },
          ],
        },
        transaction,
      );

      if (!candidate) {
        throw new NotFoundException('Candidate not found');
      }

      let newFlowStep: CandidateFlowStep;

      // Determine the flow step based on candidate state
      const hasInterviewDetails = !!(candidate as any).interview;
      const hasCompensation = !!(candidate as any).compensation;

      if (!hasInterviewDetails) {
        // No interview details yet - candidate is at invitation step
        newFlowStep = CandidateFlowStep.INVITATION;
      } else if (hasInterviewDetails && !hasCompensation) {
        // Has interview details but no compensation - next step is compensation
        newFlowStep = CandidateFlowStep.COMPENSATION;
      } else if (hasInterviewDetails && hasCompensation) {
        // Has both interview and compensation - ready for offer
        newFlowStep = CandidateFlowStep.SHARED_OFFER;
      } else {
        // Default to invitation step
        newFlowStep = CandidateFlowStep.INVITATION;
      }

      // Update the candidate's flow step if it's different
      if ((candidate as any).flowStep !== newFlowStep) {
        await this.crudHelperService.update(
          this.sequelize.models['Candidate'],
          { flowStep: newFlowStep },
          { where: { id: candidateId } },
          transaction,
        );
      }

      return newFlowStep;
    } catch (error) {
      console.error('Error updating candidate flow step:', error);
      throw error;
    }
  }

  /**
   * Gets the candidate's current flow step
   */
  async getCandidateFlowStep(candidateId: number): Promise<CandidateFlowStep> {
    const candidate = await this.crudHelperService.findOne(
      this.sequelize.models['Candidate'],
      {
        where: { id: candidateId },
        attributes: ['id', 'flowStep'],
      },
    );

    if (!candidate) {
      throw new NotFoundException('Candidate not found');
    }

    return (candidate as any).flowStep;
  }
}

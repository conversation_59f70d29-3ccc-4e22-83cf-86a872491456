import {
  IsString,
  IsEnum,
  IsDate,
  <PERSON>N<PERSON>ber,
  <PERSON><PERSON>ption<PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { LeaveRequestTypes } from '../interface/leave.request.types.interface';

export class CreateLeaveRequestDto {
  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @IsEnum(LeaveRequestTypes)
  type: LeaveRequestTypes;

  @IsString()
  reasonOfLeave: string;

  @IsOptional()
  @IsNumber()
  userId?: number;
}

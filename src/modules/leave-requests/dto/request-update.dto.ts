import { IsString, IsDate, <PERSON>NotEmpty, <PERSON><PERSON><PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { LeaveRequestTypes } from '../interface/leave.request.types.interface';

export class RequestUpdateDto {
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  startDate: Date;

  @IsDate()
  @Type(() => Date)
  @IsNotEmpty()
  endDate: Date;

  @IsString()
  @IsNotEmpty()
  reasonOfRequestUpdate: string;
}

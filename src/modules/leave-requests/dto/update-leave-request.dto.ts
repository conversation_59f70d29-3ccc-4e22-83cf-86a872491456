import { PartialType } from '@nestjs/mapped-types';
import { CreateLeaveRequestDto } from './create-leave-request.dto';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsEnum,
  IsDate,
} from 'class-validator';
import { Type } from 'class-transformer';
import { LeaveRequestTypes } from '../interface/leave.request.types.interface';
import { LeaveRequestStatuses } from '../interface/leave.request.statuses.interface';

export class UpdateLeaveRequestDto extends PartialType(CreateLeaveRequestDto) {
  @IsOptional()
  @IsNumber()
  employeeId?: number;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;

  @IsOptional()
  @IsEnum(LeaveRequestTypes)
  type?: LeaveRequestTypes;

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsEnum(LeaveRequestStatuses)
  status?: LeaveRequestStatuses;

  @IsOptional()
  @IsNumber()
  approvedBy?: number;

  @IsOptional()
  @IsString()
  reasonOfRejection?: string;
}

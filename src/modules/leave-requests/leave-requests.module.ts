import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { LeaveRequest } from '../../models/leave-request.model';
import { User } from '../../models/users-models/user.model';
import { LeaveRequestsController } from './controllers/leave-requests.controller';
import { CompanyLeaveRequestsController } from './controllers/company-leave-requests.controller';
import { LeaveRequestsService } from './services/leave-requests.service';

@Module({
  imports: [SequelizeModule.forFeature([LeaveRequest, User])],
  controllers: [LeaveRequestsController, CompanyLeaveRequestsController],
  providers: [LeaveRequestsService],
  exports: [LeaveRequestsService],
})
export class LeaveRequestsModule {}

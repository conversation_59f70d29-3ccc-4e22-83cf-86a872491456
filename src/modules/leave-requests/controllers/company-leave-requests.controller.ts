import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { GetUser } from 'src/common/decorators';
import {
  RequestUserObjectI
} from 'src/modules/auth/auth.interfaces';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import {
  CanCreate,
  CanEdit,
  CanView
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';
import { RequestUpdateDto } from '../dto/request-update.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import { LeaveRequestsService } from '../services/leave-requests.service';

@Controller('company/leave-requests')
@UseGuards(PermissionGuard)
export class CompanyLeaveRequestsController {
  constructor(private readonly leaveRequestsService: LeaveRequestsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async create(
    @GetUser() companyAdmin: RequestUserObjectI,
    @Body() createLeaveRequestDto: CreateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.createByCompanyAdmin(
      createLeaveRequestDto,
      companyAdmin,
    );
  }

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async getCompanyEmployeesLeaveRequests(
    @GetUser('companyId') companyId: number,
    @Query() dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    return this.leaveRequestsService.getCompanyEmployeesLeaveRequests(
      companyId,
      dto,
    );
  }

  @Get('dashboard-stats')
  @CanView(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async getCompanyDashboardStats(
    @GetUser('companyId') companyId: number,
  ): Promise<{
    totalLeavesThisMonth: number;
    pendingLeaveRequests: number;
    approvedToday: number;
    rejectedThisWeek: number;
  }> {
    return this.leaveRequestsService.getCompanyDashboardStats(companyId);
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async findOne(
    @Param('id') id: number,
    @GetUser('companyId') companyId: number,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.findOneByCompanyAdmin(id, companyId);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async updateById(
    @Param('id') id: number,
    @Body() updateLeaveRequestDto: UpdateLeaveRequestDto,
    @GetUser('companyId') companyId: number,
  ): Promise<Partial<LeaveRequest>> {
    // Only allow updating leave requests for own company
    return this.leaveRequestsService.updateCompanyById(
      id,
      companyId,
      updateLeaveRequestDto,
    );
  }

  @Put(':id/approve')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async approve(
    @Param('id') id: number,
    @GetUser() companyAdmin: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.approve(id, companyAdmin);
  }

  @Put(':id/reject')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async reject(
    @Param('id') id: number,
    @Body('reasonOfRejection') reasonOfRejection: string,
    @GetUser() companyAdmin: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.reject(
      id,
      companyAdmin,
      reasonOfRejection,
    );
  }

  @Put(':id/request-update')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async requestUpdate(
    @Param('id') id: number,
    @Body()
    updateRequestDto: RequestUpdateDto,
    @GetUser() companyAdmin: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.requestUpdate(
      id,
      companyAdmin,
      updateRequestDto,
    );
  }
}

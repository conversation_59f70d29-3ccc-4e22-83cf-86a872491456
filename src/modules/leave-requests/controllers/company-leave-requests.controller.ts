import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Req,
  Put,
} from '@nestjs/common';
import { LeaveRequestsService } from '../services/leave-requests.service';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import {
  AuthenticatedOnly,
  CanCreate,
  CanDelete,
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';

@Controller('company/leave-requests')
@UseGuards(PermissionGuard)
export class CompanyLeaveRequestsController {
  constructor(private readonly leaveRequestsService: LeaveRequestsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async create(
    @Req() req: UserRequestI,
    @Body() createLeaveRequestDto: CreateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    const companyAdmin = req.user;
    return this.leaveRequestsService.createByCompanyAdmin(
      createLeaveRequestDto,
      companyAdmin,
    );
  }

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async getCompanyEmployeesLeaveRequests(
    @Req() req: UserRequestI,
    @Query() dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    return this.leaveRequestsService.getCompanyEmployeesLeaveRequests(
      req.user,
      dto,
    );
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async findOne(
    @Param('id') id: number,
    @Req() req: UserRequestI,
  ): Promise<Partial<LeaveRequest>> {
    const companyAdmin = req.user;
    return this.leaveRequestsService.findOneByCompanyAdmin(id, companyAdmin);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async updateById(
    @Param('id') id: number,
    @Body() updateLeaveRequestDto: UpdateLeaveRequestDto,
    @Req() req: UserRequestI,
  ): Promise<Partial<LeaveRequest>> {
    const user = req.user;
    // Only allow updating leave requests for own company
    return this.leaveRequestsService.updateCompanyById(
      id,
      user,
      updateLeaveRequestDto,
    );
  }

  @Put(':id/approve')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async approve(
    @Param('id') id: number,
    @Req() req: UserRequestI,
  ): Promise<Partial<LeaveRequest>> {
    const companyAdmin = req.user;
    return this.leaveRequestsService.approve(id, companyAdmin);
  }

  @Put(':id/reject')
  @CanEdit(PLATFORM_SECTION_ENUM.LEAVE_MANAGEMENT)
  async reject(
    @Param('id') id: number,
    @Body('reasonOfRejection') reasonOfRejection: string,
    @Req() req: UserRequestI,
  ): Promise<Partial<LeaveRequest>> {
    const companyAdmin = req.user;
    return this.leaveRequestsService.reject(
      id,
      companyAdmin,
      reasonOfRejection,
    );
  }
}

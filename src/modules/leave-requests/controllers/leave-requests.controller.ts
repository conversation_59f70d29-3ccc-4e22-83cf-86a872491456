import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { GetUser } from 'src/common/decorators';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { AuthenticatedOnly } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import { LeaveRequestsService } from '../services/leave-requests.service';

@Controller('leave-requests')
@UseGuards(PermissionGuard)
export class LeaveRequestsController {
  constructor(private readonly leaveRequestsService: LeaveRequestsService) {}

  @Post()
  @AuthenticatedOnly()
  async create(
    @GetUser() user: RequestUserObjectI,
    @Body() createLeaveRequestDto: CreateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.create(createLeaveRequestDto, user);
  }

  @Get('paginated')
  @AuthenticatedOnly()
  async findAllPaginated(
    @GetUser('id') userId: number,
    @Query() dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    return this.leaveRequestsService.findAllPaginated(userId, dto);
  }

  @Get('balances')
  @AuthenticatedOnly()
  async getLeaveBalances(@GetUser() user: RequestUserObjectI): Promise<{
    annualLeavesBalance: number;
    sickLeaveBalance: number;
    maternityLeaveBalance: number;
    emergencyLeaveBalance: number;
  }> {
    return this.leaveRequestsService.getLeaveBalances(user);
  }

  @Get(':id')
  @AuthenticatedOnly()
  async findOne(
    @Param('id') id: number,
    @GetUser() user: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.findOneByOwner(id, user);
  }

  @Put(':id')
  @AuthenticatedOnly()
  async updateById(
    @Param('id') id: number,
    @Body() updateLeaveRequestDto: UpdateLeaveRequestDto,
    @GetUser() user: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.updateOwnById(
      id,
      user,
      updateLeaveRequestDto,
    );
  }

  @Delete(':id')
  @AuthenticatedOnly()
  async remove(
    @Param('id') id: number,
    @GetUser('id') userId: number,
  ): Promise<{ message: string }> {
    return this.leaveRequestsService.remove(id, userId);
  }
}

import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Req,
  Put,
} from '@nestjs/common';
import { LeaveRequestsService } from '../services/leave-requests.service';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { AuthenticatedOnly } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';

@Controller('leave-requests')
@UseGuards(PermissionGuard)
export class LeaveRequestsController {
  constructor(private readonly leaveRequestsService: LeaveRequestsService) {}

  @Post()
  @AuthenticatedOnly()
  async create(
    @Req() req: UserRequestI,
    @Body() createLeaveRequestDto: CreateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    return this.leaveRequestsService.create(createLeaveRequestDto, req.user);
  }

  @Get('paginated')
  @AuthenticatedOnly()
  async findAllPaginated(
    @Req() req: UserRequestI,
    @Query() dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    return this.leaveRequestsService.findAllPaginated(req.user.id, dto);
  }

  @Get(':id')
  @AuthenticatedOnly()
  async findOne(
    @Param('id') id: number,
    @Req() req: UserRequestI,
  ): Promise<Partial<LeaveRequest>> {
    const user = req.user;
    return this.leaveRequestsService.findOneByOwner(id, user);
  }

  @Put(':id')
  @AuthenticatedOnly()
  async updateById(
    @Param('id') id: number,
    @Body() updateLeaveRequestDto: UpdateLeaveRequestDto,
    @Req() req: UserRequestI,
  ): Promise<Partial<LeaveRequest>> {
    const user = req.user;
    return this.leaveRequestsService.updateOwnById(
      id,
      user,
      updateLeaveRequestDto,
    );
  }

  @Delete(':id')
  @AuthenticatedOnly()
  async remove(
    @Param('id') id: number,
    @Req() req: UserRequestI,
  ): Promise<void> {
    return this.leaveRequestsService.remove(id, req.user.id);
  }
}

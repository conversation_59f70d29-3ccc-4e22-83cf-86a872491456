import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { LeaveRequest } from '../../../models/leave-request.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';
import { Op } from 'sequelize';
import { User } from '../../../models/users-models/user.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { LeaveRequestStatuses } from '../interface/leave.request.statuses.interface';

@Injectable()
export class LeaveRequestsService {
  constructor(
    @InjectModel(LeaveRequest)
    private leaveRequestModel: typeof LeaveRequest,
    private readonly crudHelperService: CrudHelperService,
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  async create(
    createLeaveRequestDto: CreateLeaveRequestDto,
    user: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    const result = await this.crudHelperService.create<LeaveRequest>(
      this.leaveRequestModel,
      {
        ...createLeaveRequestDto,
        userId: user.id,
        companyId: user.companyId,
        createdBy: user.id,
      },
    );
    return result;
  }

  async createByCompanyAdmin(
    createLeaveRequestDto: CreateLeaveRequestDto,
    companyAdmin: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    const result = await this.crudHelperService.create<LeaveRequest>(
      this.leaveRequestModel,
      {
        ...createLeaveRequestDto,
        userId: createLeaveRequestDto.userId,
        createdBy: companyAdmin.id,
        companyId: companyAdmin.companyId,
      },
    );
    return result;
  }

  async findAllPaginated(
    userId: number,
    dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    const where: any = {
      userId,
    };

    if (dto.status) {
      where.status = dto.status;
    }

    if (dto.type) {
      where.type = dto.type;
    }

    if (dto.startDate) {
      where.startDate = { [Op.gte]: new Date(dto.startDate) };
    }

    if (dto.endDate) {
      where.endDate = { [Op.lte]: new Date(dto.endDate) };
    }

    if (dto.search) {
      where.reason = { [Op.like]: `%${dto.search}%` };
    }

    return this.crudHelperService.paginateWithQuery<LeaveRequest>(
      this.leaveRequestModel,
      {
        page: dto.page,
        limit: dto.limit,
        where,
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async getCompanyEmployeesLeaveRequests(
    user: RequestUserObjectI,
    dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    const where: any = {
      companyId: user.companyId,
    };

    if (dto.status) {
      where.status = dto.status;
    }

    if (dto.type) {
      where.type = dto.type;
    }

    if (dto.startDate) {
      where.startDate = { [Op.gte]: new Date(dto.startDate) };
    }

    if (dto.endDate) {
      where.endDate = { [Op.lte]: new Date(dto.endDate) };
    }

    if (dto.search) {
      where.reason = { [Op.like]: `%${dto.search}%` };
    }

    if (dto.userId) {
      where.userId = dto.userId;
    }

    return this.crudHelperService.paginateWithQuery<LeaveRequest>(
      this.leaveRequestModel,
      {
        page: dto.page,
        limit: dto.limit,
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profileImage',
            ],
            required: true,
          },
          {
            model: User,
            as: 'approver',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profileImage',
            ],
          },
        ],
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async findByStatus(status: string): Promise<LeaveRequest[]> {
    const results = await this.crudHelperService.findAll<LeaveRequest>(
      this.leaveRequestModel,
      {
        where: { status },
        include: { all: true },
      },
    );
    return results as LeaveRequest[];
  }

  async findOneByOwner(
    id: number,
    user: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    const where: any = { id, companyId: user.companyId, userId: user.id };

    const leaveRequest = await this.crudHelperService.findOne<LeaveRequest>(
      this.leaveRequestModel,
      {
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profileImage',
            ],
          },
        ],
      },
    );
    if (!leaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }
    return leaveRequest as Partial<LeaveRequest>;
  }

  async findOneByCompanyAdmin(
    id: number,
    user: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    const where: any = { id, companyId: user.companyId };

    const leaveRequest = await this.crudHelperService.findOne<LeaveRequest>(
      this.leaveRequestModel,
      {
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profileImage',
            ],
          },
        ],
      },
    );
    if (!leaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }
    return leaveRequest as Partial<LeaveRequest>;
  }

  async updateOwnById(
    id: number,
    user: RequestUserObjectI,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    const leaveRequest = await this.leaveRequestModel.findOne({
      where: { id, userId: user.id },
    });
    if (!leaveRequest) {
      throw new ForbiddenException(
        'You are not allowed to update this leave request',
      );
    }
    const updatedLeaveRequest =
      await this.crudHelperService.update<LeaveRequest>(
        this.leaveRequestModel,
        { ...updateLeaveRequestDto },
        { where: { id, userId: user.id }, returning: true },
      );
    return updatedLeaveRequest;
  }

  /**
   * Update a leave request by ID for company admin (can only update records of their own company)
   */
  async updateCompanyById(
    id: number,
    companyAdmin: RequestUserObjectI,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    // Only allow update if the leave request belongs to the user's company
    const leaveRequest = await this.leaveRequestModel.findOne({
      where: { id, companyId: companyAdmin.companyId },
    });
    if (!leaveRequest) {
      throw new ForbiddenException(
        'You are not allowed to update this leave request for this company',
      );
    }
    const updatedLeaveRequest =
      await this.crudHelperService.update<LeaveRequest>(
        this.leaveRequestModel,
        { ...updateLeaveRequestDto },
        { where: { id, companyId: companyAdmin.companyId }, returning: true },
      );
    return updatedLeaveRequest;
  }

  async approve(
    id: number,
    companyAdmin: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    const approverId = companyAdmin.id;
    // Only allow approve if the leave request belongs to the user's company
    const updatedLeaveRequest = await this.updateCompanyById(id, companyAdmin, {
      status: LeaveRequestStatuses.APPROVED,
      approvedBy: approverId,
    });
    return updatedLeaveRequest;
  }

  async reject(
    id: number,
    companyAdmin: RequestUserObjectI,
    reasonOfRejection: string,
  ): Promise<Partial<LeaveRequest>> {
    const approverId = companyAdmin.id;
    const updatedLeaveRequest = await this.updateCompanyById(id, companyAdmin, {
      status: LeaveRequestStatuses.REJECTED,
      approvedBy: approverId,
      reasonOfRejection,
    });
    return updatedLeaveRequest;
  }

  async remove(
    id: number,
    userId: number,
    user?: RequestUserObjectI,
  ): Promise<void> {
    const leaveRequest = await this.findOneByOwner(id, user);
    if (!leaveRequest || leaveRequest.userId !== userId) {
      throw new ForbiddenException(
        'You are not allowed to delete this leave request',
      );
    }
    await this.crudHelperService.delete<LeaveRequest>(this.leaveRequestModel, {
      where: { id },
    });
  }
}

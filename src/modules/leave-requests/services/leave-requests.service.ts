import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, WhereOptions } from 'sequelize';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { LeaveRequestUpdate } from '../../../models/leave-request-update.model';
import { LeaveRequest } from '../../../models/leave-request.model';
import { User } from '../../../models/users-models/user.model';
import { CreateLeaveRequestDto } from '../dto/create-leave-request.dto';
import { PaginatedLeavesDto } from '../dto/paginated.leaves.dto';
import { RequestUpdateDto } from '../dto/request-update.dto';
import { UpdateLeaveRequestDto } from '../dto/update-leave-request.dto';
import { LeaveRequestStatuses } from '../interface/leave.request.statuses.interface';
import { LeaveRequestUpdateStatus } from '../interface/leave.request.update.status.interface';

@Injectable()
export class LeaveRequestsService {
  constructor(
    @InjectModel(LeaveRequest)
    private leaveRequestModel: typeof LeaveRequest,
    @InjectModel(LeaveRequestUpdate)
    private leaveRequestUpdateModel: typeof LeaveRequestUpdate,
    private readonly crudHelperService: CrudHelperService,
    @InjectModel(User)
    private userModel: typeof User,
  ) {}

  async create(
    createLeaveRequestDto: CreateLeaveRequestDto,
    user: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    const result = await this.crudHelperService.create<LeaveRequest>(
      this.leaveRequestModel,
      {
        ...createLeaveRequestDto,
        userId: user.id,
        companyId: user.companyId,
        createdBy: user.id,
      },
    );
    return result;
  }

  async createByCompanyAdmin(
    createLeaveRequestDto: CreateLeaveRequestDto,
    companyAdmin: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    const result = await this.crudHelperService.create<LeaveRequest>(
      this.leaveRequestModel,
      {
        ...createLeaveRequestDto,
        userId: createLeaveRequestDto.userId,
        createdBy: companyAdmin.id,
        companyId: companyAdmin.companyId,
      },
    );
    return result;
  }

  async findAllPaginated(
    userId: number,
    dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    const where: any = {
      userId,
    };

    if (dto.status) {
      where.status = dto.status;
    }

    if (dto.type) {
      where.type = dto.type;
    }

    if (dto.startDate) {
      where.startDate = { [Op.gte]: new Date(dto.startDate) };
    }

    if (dto.endDate) {
      where.endDate = { [Op.lte]: new Date(dto.endDate) };
    }

    if (dto.search) {
      where.reason = { [Op.like]: `%${dto.search}%` };
    }

    return this.crudHelperService.paginateWithQuery<LeaveRequest>(
      this.leaveRequestModel,
      {
        page: dto.page,
        limit: dto.limit,
        where,
        order: [['createdAt', 'DESC']],
      },
    );
  }

  /**
   * Get leave balances for the authenticated user
   * Returns balance for different leave types
   */
  async getLeaveBalances(user: RequestUserObjectI): Promise<{
    annualLeavesBalance: number;
    sickLeaveBalance: number;
    maternityLeaveBalance: number;
    emergencyLeaveBalance: number;
  }> {
    // Get approved leave requests for the user
    const approvedLeaves = await this.crudHelperService.findAll<LeaveRequest>(
      this.leaveRequestModel,
      {
        where: {
          userId: user.id,
          companyId: user.companyId,
          status: LeaveRequestStatuses.APPROVED,
        },
        attributes: ['type', 'startDate', 'endDate'],
      },
    );

    // Calculate days for each leave type
    const leaveCounts = {
      ANNUAL: 0,
      SICK: 0,
      MATERNITY: 0,
      EMERGENCY: 0,
    };

    approvedLeaves.forEach((leave) => {
      const days = this.calculateDaysBetween(leave.startDate, leave.endDate);
      if (leaveCounts.hasOwnProperty(leave.type)) {
        leaveCounts[leave.type] += days;
      }
    });

    // TODO: get allocated leaves from company settings
    const allocatedLeaves = {
      ANNUAL: 25,
      SICK: 15,
      MATERNITY: 90,
      EMERGENCY: 10,
    };

    return {
      annualLeavesBalance: allocatedLeaves.ANNUAL - leaveCounts.ANNUAL,
      sickLeaveBalance: allocatedLeaves.SICK - leaveCounts.SICK,
      maternityLeaveBalance: allocatedLeaves.MATERNITY - leaveCounts.MATERNITY,
      emergencyLeaveBalance: allocatedLeaves.EMERGENCY - leaveCounts.EMERGENCY,
    };
  }

  /**
   * Get requested changes for a leave request
   */
  async getRequestedChanges(
    id: number,
    user: RequestUserObjectI,
  ): Promise<{
    originalRequest: Partial<LeaveRequest>;
    requestedChanges: {
      type?: string;
      startDate?: Date;
      endDate?: Date;
      reasonOfLeave?: string;
    } | null;
    reasonOfRequestUpdate: string | null;
    requester: User | null;
  }> {
    const leaveRequest = await this.leaveRequestModel.findOne({
      where: {
        id,
        userId: user.id,
        companyId: user.companyId,
        status: LeaveRequestStatuses.REQUEST_UPDATE,
      },
    });

    if (!leaveRequest) {
      throw new NotFoundException(
        'Leave request not found or not in update status',
      );
    }

    // Get the pending update request
    const updateRequest = await this.leaveRequestUpdateModel.findOne({
      where: {
        leaveRequestId: id,
        status: LeaveRequestUpdateStatus.PENDING,
      },
      include: [
        {
          model: User,
          as: 'requester',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
    });

    return {
      originalRequest: leaveRequest,
      requestedChanges: updateRequest
        ? {
            startDate: updateRequest.startDate,
            endDate: updateRequest.endDate,
          }
        : null,
      reasonOfRequestUpdate: updateRequest?.reasonOfRequestUpdate || null,
      requester: updateRequest?.requester || null,
    };
  }

  /**
   * Get company dashboard statistics for leave requests
   * Returns counts for various metrics
   */
  async getCompanyDashboardStats(companyId: number): Promise<{
    totalLeavesThisMonth: number;
    pendingLeaveRequests: number;
    approvedToday: number;
    rejectedThisWeek: number;
  }> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const startOfToday = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
    );
    const startOfWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get total leaves this month (approved requests that overlap with current month)
    const totalLeavesThisMonth = await this.leaveRequestModel.count({
      where: {
        companyId,
        status: LeaveRequestStatuses.APPROVED,
        startDate: { [Op.lte]: endOfMonth },
        endDate: { [Op.gte]: startOfMonth },
      },
    });

    // Get pending leave requests
    const pendingLeaveRequests = await this.leaveRequestModel.count({
      where: {
        companyId,
        status: LeaveRequestStatuses.PENDING,
      },
    });

    // Get approved today
    const approvedToday = await this.leaveRequestModel.count({
      where: {
        companyId,
        status: LeaveRequestStatuses.APPROVED,
        updatedAt: { [Op.gte]: startOfToday },
      },
    });

    // Get rejected this week
    const rejectedThisWeek = await this.leaveRequestModel.count({
      where: {
        companyId,
        status: LeaveRequestStatuses.REJECTED,
        updatedAt: { [Op.gte]: startOfWeek },
      },
    });

    return {
      totalLeavesThisMonth,
      pendingLeaveRequests,
      approvedToday,
      rejectedThisWeek,
    };
  }

  /**
   * Calculate days between two dates (inclusive)
   */
  private calculateDaysBetween(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const timeDiff = end.getTime() - start.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 for inclusive
    return daysDiff;
  }

  async getCompanyEmployeesLeaveRequests(
    companyId: number,
    dto: PaginatedLeavesDto,
  ): Promise<PaginatedResult<Partial<LeaveRequest>>> {
    const where: any = {
      companyId,
    };

    if (dto.status) {
      where.status = dto.status;
    }

    if (dto.type) {
      where.type = dto.type;
    }

    if (dto.startDate) {
      where.startDate = { [Op.gte]: new Date(dto.startDate) };
    }

    if (dto.endDate) {
      where.endDate = { [Op.lte]: new Date(dto.endDate) };
    }

    if (dto.search) {
      where.reason = { [Op.like]: `%${dto.search}%` };
    }

    if (dto.userId) {
      where.userId = dto.userId;
    }

    return this.crudHelperService.paginateWithQuery<LeaveRequest>(
      this.leaveRequestModel,
      {
        page: dto.page,
        limit: dto.limit,
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profileImage',
            ],
            required: true,
          },
          {
            model: User,
            as: 'approver',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profileImage',
            ],
          },
        ],
        order: [['createdAt', 'DESC']],
      },
    );
  }

  async findByStatus(status: string): Promise<LeaveRequest[]> {
    const results = await this.crudHelperService.findAll<LeaveRequest>(
      this.leaveRequestModel,
      {
        where: { status },
        include: { all: true },
      },
    );
    return results as LeaveRequest[];
  }

  /**
   * Get a leave request by ID for the current user, including any pending update request
   */
  async findOneByOwner(id: number, user: RequestUserObjectI): Promise<any> {
    const leaveRequest = await this.leaveRequestModel.findOne({
      where: { id, companyId: user.companyId, userId: user.id },
    });
    if (!leaveRequest) {
      throw new NotFoundException('Leave request not found');
    }
    // Find any pending update request
    const pendingUpdateRequest = await this.leaveRequestUpdateModel.findOne({
      where: {
        leaveRequestId: id,
        status: LeaveRequestUpdateStatus.PENDING,
      },
      include: [
        {
          model: User,
          as: 'requester',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
    });
    return {
      leaveRequest,
      pendingUpdateRequest,
    };
  }

  async findOneByCompanyAdmin(
    id: number,
    companyId: number,
  ): Promise<Partial<LeaveRequest>> {
    const where: WhereOptions<LeaveRequest> = { id, companyId };

    const leaveRequest = await this.crudHelperService.findOne<LeaveRequest>(
      this.leaveRequestModel,
      {
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profileImage',
            ],
          },
        ],
      },
    );
    if (!leaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }
    return leaveRequest as Partial<LeaveRequest>;
  }

  /**
   * Update a leave request by ID for the current user (can only update their own record)
   * If there is a pending update request, mark it as completed
   */
  async updateOwnById(
    id: number,
    user: RequestUserObjectI,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    const leaveRequest = await this.leaveRequestModel.findOne({
      where: { id, userId: user.id },
    });
    if (!leaveRequest) {
      throw new ForbiddenException(
        'You are not allowed to update this leave request',
      );
    }
    await this.leaveRequestUpdateModel.update(
      { status: LeaveRequestUpdateStatus.COMPLETED },
      {
        where: {
          leaveRequestId: id,
          status: LeaveRequestUpdateStatus.PENDING,
        },
      },
    );

    const updatedLeaveRequest =
      await this.crudHelperService.update<LeaveRequest>(
        this.leaveRequestModel,
        {
          ...updateLeaveRequestDto,
          status: LeaveRequestStatuses.REVIEW_UPDATE,
        },
        { where: { id, userId: user.id }, returning: true },
      );
    return updatedLeaveRequest;
  }

  /**
   * Update a leave request by ID for company admin (can only update records of their own company)
   */
  async updateCompanyById(
    id: number,
    companyId: number,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    // Only allow update if the leave request belongs to the user's company

    const leaveRequest = await this.leaveRequestModel.findOne({
      where: { id, companyId },
    });
    if (!leaveRequest) {
      throw new ForbiddenException(
        'You are not allowed to update this leave request for this company',
      );
    }
    const updatedLeaveRequest =
      await this.crudHelperService.update<LeaveRequest>(
        this.leaveRequestModel,
        { ...updateLeaveRequestDto },
        { where: { id, companyId }, returning: true },
      );
    return updatedLeaveRequest;
  }

  /**
   * Company admin requests user to update their leave request
   */
  async requestUpdate(
    id: number,
    companyAdmin: RequestUserObjectI,
    updateRequestDto: RequestUpdateDto,
  ): Promise<Partial<LeaveRequest>> {
    const leaveRequest = await this.leaveRequestModel.findOne({
      where: { id, companyId: companyAdmin.companyId },
    });

    if (!leaveRequest) {
      throw new NotFoundException('Leave request not found');
    }

    // Create a new update request record with separate fields
    await this.leaveRequestUpdateModel.create({
      leaveRequestId: id,
      requestedBy: companyAdmin.id,
      ...updateRequestDto,
    });

    // Update the leave request status to REQUEST_UPDATE
    const updatedLeaveRequest = await this.leaveRequestModel.update(
      {
        status: LeaveRequestStatuses.REQUEST_UPDATE,
      },
      {
        where: { id, companyId: companyAdmin.companyId },
        returning: true,
      },
    );

    return updatedLeaveRequest[1][0];
  }
  /*
   * User updates their leave request after company admin requested changes
   */
  async updateRequestedLeave(
    id: number,
    user: RequestUserObjectI,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
  ): Promise<Partial<LeaveRequest>> {
    // Check if leave request exists and belongs to user
    const leaveRequest = await this.leaveRequestModel.findOne({
      where: {
        id,
        userId: user.id,
        companyId: user.companyId,
        status: LeaveRequestStatuses.REQUEST_UPDATE,
      },
    });

    if (!leaveRequest) {
      throw new NotFoundException(
        'Leave request not found or not in update status',
      );
    }

    // Check if there's a pending update request
    const updateRequest = await this.leaveRequestUpdateModel.findOne({
      where: {
        leaveRequestId: id,
        status: LeaveRequestUpdateStatus.PENDING,
      },
    });

    if (!updateRequest) {
      throw new NotFoundException('No pending update request found');
    }

    // Update the leave request with user's new values
    await this.leaveRequestModel.update(
      {
        ...updateLeaveRequestDto,
        status: LeaveRequestStatuses.PENDING,
      },
      {
        where: {
          id,
          userId: user.id,
          companyId: user.companyId,
          status: LeaveRequestStatuses.REQUEST_UPDATE,
        },
      },
    );

    // Mark the update request as completed
    await this.leaveRequestUpdateModel.update(
      {
        status: LeaveRequestUpdateStatus.COMPLETED,
      },
      {
        where: {
          leaveRequestId: id,
          status: LeaveRequestUpdateStatus.PENDING,
        },
      },
    );

    return this.findOneByOwner(id, user);
  }

  async approve(
    id: number,
    companyAdmin: RequestUserObjectI,
  ): Promise<Partial<LeaveRequest>> {
    const approverId = companyAdmin.id;
    // Only allow approve if the leave request belongs to the user's company
    const updatedLeaveRequest = await this.updateCompanyById(
      id,
      companyAdmin.companyId,
      {
        status: LeaveRequestStatuses.APPROVED,
        approvedBy: approverId,
      },
    );
    return updatedLeaveRequest;
  }

  async reject(
    id: number,
    companyAdmin: RequestUserObjectI,
    reasonOfRejection: string,
  ): Promise<Partial<LeaveRequest>> {
    const approverId = companyAdmin.id;
    const updatedLeaveRequest = await this.updateCompanyById(
      id,
      companyAdmin.companyId,
      {
        status: LeaveRequestStatuses.REJECTED,
        approvedBy: approverId,
        reasonOfRejection,
      },
    );
    return updatedLeaveRequest;
  }

  async remove(id: number, userId: number): Promise<{ message: string }> {
    const deletedLeaveRequest =
      await this.crudHelperService.delete<LeaveRequest>(
        this.leaveRequestModel,
        {
          where: { id, userId },
        },
      );

    if (!deletedLeaveRequest) {
      throw new ForbiddenException(
        'You are not allowed to delete this leave request',
      );
    }
    return { message: 'Leave request deleted successfully' };
  }
}

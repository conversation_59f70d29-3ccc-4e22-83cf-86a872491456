import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { User } from 'src/models/users-models/user.model';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { CompanyService } from 'src/modules/company/services/company.service';
import { UsersService } from 'src/modules/users/services/users.service';
import { RedirectSectionEnum } from 'src/utils/redirect-section.enum';
import { Department } from '../../../models/department.model';
import { CreateDepartmentDto } from '../dto/create-department.dto';
import { UpdateDepartmentDto } from '../dto/update-department.dto';

@Injectable()
export class DepartmentsService {
  constructor(
    @InjectModel(Department)
    private departmentModel: typeof Department,
    @InjectModel(EmploymentDetails)
    private employmentDetailsModel: typeof EmploymentDetails,
    @InjectModel(User)
    private userModel: typeof User,
    private readonly crudHelperService: CrudHelperService,
    private readonly companyService: CompanyService,
    private readonly usersService: UsersService,
    private readonly sequelize: Sequelize,
  ) {}

  async findAll(userId: number): Promise<Partial<Department>[]> {
    const company = await this.companyService.findByUserId(userId);

    return this.crudHelperService.findAll<Department>(this.departmentModel, {
      where: { companyId: company.id },
      include: [
        {
          model: User,
          as: 'manager',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
    });
  }

  async findOne(id: number): Promise<Partial<Department>> {
    return this.crudHelperService.findOne<Department>(this.departmentModel, {
      where: { id },
      include: [
        {
          model: User,
          as: 'manager',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
    });
  }

  async create(
    userId: number,
    createDepartmentDto: CreateDepartmentDto,
  ): Promise<Partial<Department>> {
    const transaction = await this.sequelize.transaction();

    try {
      const company = await this.companyService.findByUserId(userId);
      const user = await this.usersService.findUserBy({
        query: { id: userId },
      });
      const isDashboardRedirect =
        user.redirectTo === RedirectSectionEnum.DASHBOARD;

      if (!isDashboardRedirect) {
        await this.usersService.updateUser(
          user.id,
          { redirectTo: RedirectSectionEnum.DASHBOARD },
          transaction,
        );
      }

      const department = await this.crudHelperService.create<Department>(
        this.departmentModel,
        {
          ...createDepartmentDto,
          companyId: company.id,
        },
        transaction,
      );

      await transaction.commit();
      return department;
    } catch (error) {
      await transaction.rollback();
      console.log('Transaction rolled back due to error:', error.message);
      throw error;
    }
  }

  async update(
    id: number,
    updateDepartmentDto: UpdateDepartmentDto,
  ): Promise<Partial<Department>> {
    await this.findOne(id);
    return this.crudHelperService.update<Department>(
      this.departmentModel,
      updateDepartmentDto,
      {
        where: { id },
      },
    );
  }

  async remove(id: number): Promise<void> {
    await this.findOne(id);
    await this.crudHelperService.delete(this.departmentModel, {
      where: { id },
    });
  }

  async getAllDepartmentsByPlatformSuperAdminPaginated(
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<Department>>> {
    return this.crudHelperService.paginateWithQuery<Department>(
      this.departmentModel,
      {
        page,
        limit,
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: User,
            as: 'manager',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
      },
    );
  }

  async getDepartmentsPaginated(
    userId: number,
    page: number,
    limit: number,
  ): Promise<PaginatedResult<Partial<Department>>> {
    const user = await this.crudHelperService.findOne<User>(this.userModel, {
      where: { id: userId },
    });

    if (!user?.companyId) {
      throw new NotFoundException('User is not associated with any company');
    }

    return this.crudHelperService.paginateWithQuery<Department>(
      this.departmentModel,
      {
        page,
        limit,
        where: { companyId: user.companyId },
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: User,
            as: 'manager',
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
      },
    );
  }
}

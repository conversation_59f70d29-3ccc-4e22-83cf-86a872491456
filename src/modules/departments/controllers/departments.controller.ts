import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import { PaginatedResult } from 'src/common/crud-helper/crud-helper.service';
import { GetUser } from 'src/common/decorators';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { Department } from '../../../models/department.model';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import {
  CanCreate,
  CanDelete,
  CanEdit,
  CanView,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { CreateDepartmentDto } from '../dto/create-department.dto';
import { UpdateDepartmentDto } from '../dto/update-department.dto';
import { DepartmentsService } from '../services/departments.service';

@Controller('departments')
@UseGuards(PermissionGuard)
export class DepartmentsController {
  constructor(private readonly departmentsService: DepartmentsService) {}

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  findAllPaginated(
    @GetUser('id') userId: number,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<PaginatedResult<Partial<Department>>> {
    return this.departmentsService.getDepartmentsPaginated(
      userId,
      Number(page),
      Number(limit),
    );
  }

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  findAll(@GetUser('id') userId: number): Promise<Partial<Department>[]> {
    return this.departmentsService.findAll(userId);
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  findOne(@Param('id', ParseIntPipe) id: number): Promise<Partial<Department>> {
    return this.departmentsService.findOne(id);
  }

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  async create(
    @Body() createDepartmentDto: CreateDepartmentDto,
    @GetUser('id') userId: number,
  ): Promise<{ message: string; data: Partial<Department> }> {
    const createdDepartment = await this.departmentsService.create(
      userId,
      createDepartmentDto,
    );

    return {
      message: 'Department created successfully',
      data: createdDepartment,
    };
  }

  @Patch(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDepartmentDto: UpdateDepartmentDto,
  ): Promise<Partial<Department>> {
    return this.departmentsService.update(id, updateDepartmentDto);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.departmentsService.remove(id);
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards
} from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import {
  CanCreate,
  CanDelete,
  CanEdit,
  CanView,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { CreateCompensationDto } from '../dto/create-compensation.dto';
import { UpdateCompensationDto } from '../dto/update-compensation.dto';
import { CompensationsService } from '../services/compensations.service';

@Controller('compensations')
@UseGuards(PermissionGuard)
export class CompensationsController {
  constructor(private readonly compensationsService: CompensationsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  async create(
    @Body() dto: CreateCompensationDto,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.compensationsService.create(dto, companyId);
    return {
      success: true,
      message: 'Compensation and benefits created successfully',
      data: result,
    };
  }

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  async findAll(@GetUser('companyId') companyId: number) {
    const result = await this.compensationsService.findAll(companyId);
    return {
      success: true,
      message: 'Compensations retrieved successfully',
      data: result,
    };
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.compensationsService.findOne(id, companyId);
    return {
      success: true,
      message: 'Compensation retrieved successfully',
      data: result,
    };
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateCompensationDto,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.compensationsService.update(id, dto, companyId);
    return {
      success: true,
      message: 'Compensation updated successfully',
      data: result,
    };
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @GetUser('companyId') companyId: number,
  ) {
    await this.compensationsService.remove(id, companyId);
    return {
      success: true,
      message: 'Compensation deleted successfully',
    };
  }

  @Get('candidate/:candidateId')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async findByCandidateId(
    @Param('candidateId', ParseIntPipe) candidateId: number,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.compensationsService.findByCandidateId(
      candidateId,
      companyId,
    );
    return {
      success: true,
      message: 'Candidate compensation retrieved successfully',
      data: result,
    };
  }
}

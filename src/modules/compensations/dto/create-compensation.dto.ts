import {
  IsDateString,
  IsString,
  IsNumber,
  IsOptional,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class CreateBenefitNestedDto {
  @IsString()
  allowance: string;

  @IsString()
  frequency: string;

  @IsNumber()
  @Type(() => Number)
  amount: number;
}

export class CreateCompensationDto {
  @IsNumber()
  @IsOptional()
  candidateId: number;

  @IsNumber()
  @IsOptional()
  employeeId: number;

  @IsDateString()
  effectiveFrom: Date;

  @IsDateString()
  @IsOptional()
  reviewDate?: Date;

  @IsString()
  currency: string;

  @IsString()
  frequency: string;

  @IsNumber()
  totalSalary: number;

  @IsNumber()
  basicSalary: number;

  @IsNumber()
  @IsOptional()
  others?: number;

  @IsNumber()
  @IsOptional()
  housing?: number;

  @IsNumber()
  @IsOptional()
  transportation?: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateBenefitNestedDto)
  @IsOptional()
  benefits?: CreateBenefitNestedDto[];
}

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Compensation } from '../../../models/users-models/compensation.model';
import { Benefit } from '../../../models/users-models/benefits.model';
import { Candidate } from '../../../models/users-models/candidate.model';
import { CreateCompensationDto } from '../dto/create-compensation.dto';
import { UpdateCompensationDto } from '../dto/update-compensation.dto';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';
import { CandidateFlowStep } from 'src/modules/users/enums/candidate-flow-step.enum';
// Removed: import { TenantAwareBaseService } from '../../../common/services/tenant-aware-base.service';
// Removed: import { TenantContextI } from '../../../common/types/tenant-context.types';

@Injectable()
export class CompensationsService {
  constructor(
    @InjectModel(Compensation)
    private compensationModel: typeof Compensation,
    @InjectModel(Benefit)
    private benefitModel: typeof Benefit,
    @InjectModel(Candidate)
    private candidateModel: typeof Candidate,
    private sequelize: Sequelize,
    private crudHelperService: CrudHelperService,
  ) {}

  async create(
    dto: CreateCompensationDto,
    companyId: number,
  ): Promise<Compensation> {
    const transaction = await this.sequelize.transaction();
    try {
      // Validate that candidate belongs to the company
      if (dto.employeeId) {
        if (dto.candidateId) {
          throw new NotFoundException(
            'Cannot have both employee and candidate',
          );
        }
        const employee = await this.crudHelperService.findOne(
          this.sequelize.models['User'],
          {
            where: {
              id: dto.employeeId,
              companyId,
            },
          },
        );
        if (!employee) {
          throw new NotFoundException('Employee not found or not authorized');
        }
      } else {
        if (!dto.candidateId) {
          throw new NotFoundException('Must have either employee or candidate');
        }
        const candidate = await this.crudHelperService.findOne(
          this.candidateModel,
          {
            where: {
              id: dto.candidateId,
              companyId,
            },
          },
        );
        if (!candidate) {
          throw new NotFoundException('Candidate not found or not authorized');
        }
      }
      // Extract benefits from DTO
      const { benefits, ...compensationData } = dto;
      // Create compensation
      const compensation = await this.crudHelperService.create(
        this.compensationModel,
        compensationData,
        transaction,
      );
      // Create benefits if provided
      if (benefits && benefits.length > 0) {
        const benefitsData = benefits.map((benefit) => ({
          ...benefit,
          compensationId: (compensation as any).id,
        }));
        await this.benefitModel.bulkCreate(benefitsData, { transaction });
      }

      // Update can didate flow step to COMPENSATION after compensation is created
      await this.candidateModel.update(
        { flowStep: CandidateFlowStep.SHARED_OFFER },
        { where: { id: dto.candidateId }, transaction },
      );

      await transaction.commit();
      // Return compensation with benefits
      return (await this.compensationModel.findByPk((compensation as any).id, {
        include: [
          { model: Benefit },
          {
            model: Candidate,
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
      })) as Compensation;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async findOne(id: number, companyId: number): Promise<Compensation> {
    const record = await this.crudHelperService.findOne(
      this.compensationModel,
      {
        where: { id },
        include: [
          {
            model: Candidate,
            where: { companyId },
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
          { model: Benefit },
        ],
      },
    );
    if (!record) throw new NotFoundException('Not found or not authorized');
    return record as Compensation;
  }

  async update(
    id: number,
    dto: UpdateCompensationDto,
    companyId: number,
  ): Promise<Compensation> {
    // Ensure the compensation belongs to the company
    const record = await this.findOne(id, companyId);
    await this.crudHelperService.update(this.compensationModel, dto, {
      where: { id },
    });
    return (await this.findOne(id, companyId)) as Compensation;
  }

  async remove(id: number, companyId: number): Promise<void> {
    // Ensure the compensation belongs to the company
    await this.findOne(id, companyId);
    await this.crudHelperService.delete(this.compensationModel, {
      where: { id },
    });
  }

  async findByCandidateId(
    candidateId: number,
    companyId: number,
  ): Promise<Compensation> {
    // Check if candidate belongs to the same company
    const candidate = await this.crudHelperService.findOne(
      this.candidateModel,
      {
        where: {
          id: candidateId,
          companyId,
        },
      },
    );
    if (!candidate) {
      throw new NotFoundException('Candidate does not belong to your company');
    }
    const compensation = await this.crudHelperService.findOne(
      this.compensationModel,
      {
        where: { candidateId },
        include: [
          { model: Benefit },
          {
            model: Candidate,
            attributes: ['id', 'firstName', 'lastName', 'email'],
          },
        ],
      },
    );
    if (!compensation) {
      throw new NotFoundException('Compensation not found');
    }
    return compensation as Compensation;
  }

  async findAll(companyId: number): Promise<Compensation[]> {
    return (await this.crudHelperService.findAll(this.compensationModel, {
      include: [
        {
          model: Candidate,
          where: { companyId },
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        { model: Benefit },
      ],
    })) as Compensation[];
  }
}

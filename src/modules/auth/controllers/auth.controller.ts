import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { GetUser } from 'src/common/decorators';
import {
  AuthenticatedOnly,
  PublicRoute,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import {
  ThrottleRelaxed,
  ThrottleStrict,
} from '../../throttling/decorators/throttle.decorators';
import { LoginDto } from '../dto';
import { ResendOtpDto } from '../dto/resend-otp.dto';
import { SignupDto } from '../dto/signup.dto';
import { VerifyEmailDto } from '../dto/verify-email.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { LocalAuthGuard } from '../guards/local-auth.guard';
import { AuthService } from '../services/auth.service';
import { CookieService } from '../services/cookie.service';
import { GoogleOAuthService } from '../services/google-oauth.service';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly googleOAuthService: GoogleOAuthService,
    private readonly cookieService: CookieService,
  ) {}

  @PublicRoute()
  @Post('signup')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async signup(@Body() signupDto: SignupDto) {
    const data = await this.authService.userSignup(signupDto);

    return {
      message: 'Signup successful',
      data: data,
    };
  }

  @PublicRoute()
  @UseGuards(LocalAuthGuard)
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async login(@Body() loginDto: LoginDto) {
    const data = await this.authService.userLogin(loginDto);

    return {
      message: 'Login successful',
      data: data,
    };
  }

  @AuthenticatedOnly()
  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ThrottleRelaxed()
  async getProfile(@GetUser('id') userId: number) {
    return this.authService.getUserProfile(userId);
  }

  @PublicRoute()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async refresh(@Body('refreshToken') refreshToken: string) {
    const data = await this.authService.refreshTokens(refreshToken);

    return {
      message: 'Token refreshed successfully',
      data: data,
    };
  }

  @PublicRoute()
  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async verifyEmail(@Body() dto: VerifyEmailDto) {
    const result = await this.authService.verifyEmailOtp(dto);
    return {
      message: result.message,
    };
  }

  @PublicRoute()
  @Post('resend-otp')
  @HttpCode(HttpStatus.OK)
  @ThrottleStrict()
  async resendOtp(@Body() dto: ResendOtpDto) {
    const result = await this.authService.resendOtp(dto);
    return {
      message: result.message,
    };
  }

  // --- Google OAuth Endpoints ---

  @PublicRoute()
  @Get('google')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async googleAuth() {
    const configStatus = this.googleOAuthService.getConfigStatus();

    if (!configStatus.isConfigured) {
      return {
        message: 'Google OAuth is not configured',
        error:
          'Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables',
        status: 'disabled',
        config: configStatus,
      };
    }

    return {
      message: 'Google OAuth is configured',
      loginUrl: '/api/auth/google/login',
      status: 'enabled',
      config: configStatus,
    };
  }

  @PublicRoute()
  @Get('google/login')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async googleLogin(@Res() res: Response) {
    const authUrl = this.googleOAuthService.generateAuthUrl();

    return res.status(200).json({
      message: 'Google OAuth flow initiated',
      authUrl: authUrl,
      status: 'enabled',
      instructions: 'Visit the authUrl to start Google OAuth login',
    });
  }

  @PublicRoute()
  @Get('google/callback')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async googleCallback(
    @Query('code') code: string,
    @Query('error') error: string,
    @Res() res: Response,
  ) {
    // Handle OAuth errors
    if (error) {
      const errorUrl = this.googleOAuthService.generateErrorRedirectUrl(error);
      return res.redirect(errorUrl);
    }

    // Check for authorization code
    if (!code) {
      const errorUrl = this.googleOAuthService.generateErrorRedirectUrl(
        'no_authorization_code',
      );
      return res.redirect(errorUrl);
    }

    // Process Google OAuth callback
    const googleUserData = await this.googleOAuthService.processCallback(code);

    // Validate or create user
    const result =
      await this.authService.validateOrCreateGoogleUser(googleUserData);

    // Set authentication cookies
    const userData = {
      id: result.id,
      email: result.email,
      firstName: result.firstName,
      lastName: result.lastName,
      profileImage: result.profileImage,
      role: {
        id: result.role.id,
        name: result.role.name,
        scope: result.role.scope,
      },
      redirectTo: result.redirectTo,
    };

    this.cookieService.setAuthCookies(
      res,
      result.accessToken,
      result.refreshToken,
      userData,
      'user',
    );

    // Redirect to frontend with success status
    const successUrl = this.googleOAuthService.generateSuccessRedirectUrl();
    return res.redirect(successUrl);
  }

  @PublicRoute()
  @Get('google/status')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async googleStatus() {
    const configStatus = this.googleOAuthService.getConfigStatus();

    return {
      message: 'Google OAuth Status',
      data: {
        ...configStatus,
        loginUrl: '/api/auth/google/login',
      },
      status: 'success',
    };
  }

  @PublicRoute()
  @Get('google/check')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async googleCheck() {
    const configStatus = this.googleOAuthService.getConfigStatus();

    return {
      message: 'Google OAuth Status Check',
      data: {
        ...configStatus,
        loginUrl: '/api/auth/google/login',
      },
      status: 'success',
    };
  }

  @PublicRoute()
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async logout(@Res() res: Response) {
    // Clear all authentication cookies
    this.cookieService.clearAuthCookies(res);

    return res.json({
      message: 'Logged out successfully',
      status: 'success',
    });
  }
}

import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';

import { GetUser } from 'src/common/decorators';
import { PublicRoute } from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from '../../throttling/decorators/throttle.decorators';
import { AdminRequestI } from '../auth.interfaces';
import { AdminLoginDto } from '../dto/admin-login.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { AuthService } from '../services/auth.service';

@Controller('admin')
export class AdminAuthController {
  constructor(private readonly authService: AuthService) {}

  @PublicRoute()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async login(@Body() loginDto: AdminLoginDto) {
    const data = await this.authService.adminLogin(loginDto);

    return {
      message: 'Login successful',
      data: data,
    };
  }

  @PublicRoute()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async refresh(@Body('refreshToken') refreshToken: string) {
    const data = await this.authService.refreshTokens(refreshToken);

    return {
      message: 'Token refreshed successfully',
      data: data,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get('current')
  @ThrottleRelaxed()
  async getCurrentAdmin(@GetUser('id') userId: number) {
    const currentUserData = await this.authService.getCurrentAdmin(userId);

    if (!currentUserData) {
      throw new UnauthorizedException('Admin not found');
    }
    const { password: _, ...userData } = currentUserData;

    return {
      message: 'Current admin retrieved successfully',
      data: userData,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @ThrottleRelaxed()
  async logout(@Req() req: AdminRequestI) {
    const token = req.headers?.['authorization']?.split(' ')[1];

    await this.authService.invalidateToken(token);

    return {
      message: 'Logout successful',
    };
  }
}

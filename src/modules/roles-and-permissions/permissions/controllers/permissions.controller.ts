import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import {
  IPermissionCheck,
  IUserPermissions,
} from 'src/modules/auth/auth.interfaces';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { PermissionService } from '../permission.service';

@Controller('permissions')
@UseGuards(PermissionGuard)
export class PermissionsController {
  constructor(private readonly permissionService: PermissionService) {}

  /**
   * Get current user's permissions
   */
  @Get('my-permissions')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async getMyPermissions(
    @GetUser('id') userId: number,
  ): Promise<IUserPermissions | null> {
    return this.permissionService.getUserPermissions(userId);
  }

  /**
   * Get current user's accessible sections
   */
  @Get('accessible-sections')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async getAccessibleSections(
    @GetUser('id') userId: number,
  ): Promise<string[]> {
    return this.permissionService.getUserAccessibleSections(userId);
  }

  /**
   * Check if user has specific permission
   */
  @Post('check')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async checkPermission(
    @GetUser('id') userId: number,
    @Body() permissionCheck: IPermissionCheck,
  ): Promise<{ hasPermission: boolean }> {
    const hasPermission = await this.permissionService.checkPermission(
      userId,
      permissionCheck.section,
      permissionCheck.action,
      permissionCheck.companyId,
    );

    return { hasPermission };
  }

  /**
   * Check multiple permissions at once
   */
  @Post('check-multiple')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async checkMultiplePermissions(
    @GetUser('id') userId: number,
    @Body() permissionChecks: IPermissionCheck[],
  ): Promise<{ [key: string]: boolean }> {
    return this.permissionService.checkMultiplePermissions(
      userId,
      permissionChecks,
    );
  }

  /**
   * Get user's company ID
   */
  @Get('company-id')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async getCompanyId(
    @GetUser('id') userId: number,
  ): Promise<{ companyId: number | null }> {
    const companyId = await this.permissionService.getUserCompanyId(userId);
    return { companyId };
  }

  /**
   * Check if user has platform scope
   */
  @Get('platform-scope')
  // @CanViewPlatform(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async hasPlatformScope(
    @GetUser('id') userId: number,
  ): Promise<{ hasPlatformScope: boolean }> {
    const hasPlatformScope =
      await this.permissionService.hasPlatformScope(userId);
    return { hasPlatformScope };
  }

  /**
   * Get all available platform sections
   */
  @Get('platform-sections')
  // @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  @HttpCode(HttpStatus.OK)
  async getPlatformSections(): Promise<{ sections: string[] }> {
    const sections = Object.values(PLATFORM_SECTION_ENUM);
    return { sections };
  }
}

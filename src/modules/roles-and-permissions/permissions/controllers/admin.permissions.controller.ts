import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { PermissionService } from '../permission.service';
import { Permission } from 'src/models/roles-and-permissions/permission.model';

@Controller('admin/permissions')
export class AdminPermissionsController {
  constructor(private readonly permissionService: PermissionService) {}

  @Post()
  create(@Body() dto: Partial<Permission>) {
    return this.permissionService.create(dto);
  }

  @Get()
  findAll() {
    return this.permissionService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.permissionService.findById(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() dto: Partial<Permission>) {
    return this.permissionService.update(+id, dto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.permissionService.delete(+id);
  }
}

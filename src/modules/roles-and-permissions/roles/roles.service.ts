import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import {
  CrudHelperService,
  PaginatedResult,
} from 'src/common/crud-helper/crud-helper.service';
import { RedisCacheService } from 'src/common/services/redis-cache.service';
import {
  PlatformSection,
  PlatformSectionScopeEnum,
} from 'src/models/platform-section.model';
import { Permission } from 'src/models/roles-and-permissions/permission.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { User } from 'src/models/users-models/user.model';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { CompanyService } from 'src/modules/company/services/company.service';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { PermissionLevelEnum } from '../permissions/enums/enum';
import { PlatformSectionsService } from '../platform-sections/platform-sections.service';
import { FindAllRolesDto } from './dto/find.all.dto';
import { SetPermissionsDto } from './dto/set-permissions.dto';
import { UserFindAllRolesDto } from './dto/user.find.all.dto';
import { ROLES_ENUM } from './utils/enums';

@Injectable()
export class RolesService {
  private readonly SUPER_ADMIN_CACHE_TTL = 30 * 60; // 30 minutes in seconds
  private readonly SUPER_ADMIN_CACHE_KEY = 'super-admin-role';

  constructor(
    @InjectModel(Role) private readonly roleModel: typeof Role,
    @InjectModel(Permission)
    private readonly permissionModel: typeof Permission,
    @InjectModel(PlatformSection)
    private readonly platformSectionModel: typeof PlatformSection,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(EmploymentDetails)
    private readonly employmentDetailsModel: typeof EmploymentDetails,
    @InjectModel(User)
    private readonly userModel: typeof User,
    private readonly crudService: CrudHelperService,
    private readonly companyService: CompanyService,
    private readonly redisCache: RedisCacheService,
    private readonly platformSectionsService: PlatformSectionsService,
  ) {}

  /**
   * Get the Super Admin role (cached in Redis for performance)
   */
  async getPlatformSuperAdminRole(): Promise<Partial<Role> | null> {
    try {
      // Try to get from Redis cache first
      const cachedRole = await this.redisCache.get<Partial<Role>>(
        this.SUPER_ADMIN_CACHE_KEY,
      );

      if (cachedRole) {
        return cachedRole;
      }

      // Fetch Platform Super Admin role from database
      const superAdminRole = await this.crudService.findOne<Role>(
        this.roleModel,
        {
          where: {
            name: ROLES_ENUM.PLATFORM_SUPER_ADMIN,
            scope: ROLE_SCOPE_ENUM.PLATFORM,
          },
        },
      );

      // Cache the result in Redis
      if (superAdminRole) {
        await this.redisCache.set(
          this.SUPER_ADMIN_CACHE_KEY,
          superAdminRole,
          this.SUPER_ADMIN_CACHE_TTL,
        );
      }

      return superAdminRole;
    } catch (error) {
      console.error('Error fetching Super Admin role:', error);
      return null;
    }
  }

  // get the company super admin role
  async getCompanySuperAdminRole(): Promise<Partial<Role> | null> {
    return this.crudService.findOne<Role>(this.roleModel, {
      where: {
        name: ROLES_ENUM.COMPANY_SUPER_ADMIN,
        scope: ROLE_SCOPE_ENUM.COMPANY,
      },
    });
  }

  /**
   * Check if user is Super Admin by comparing role IDs
   */
  async isUserSuperAdmin(userId: number): Promise<boolean> {
    try {
      // Get Super Admin role
      const superAdminRole = await this.getPlatformSuperAdminRole();
      if (!superAdminRole) {
        return false;
      }

      // Get user's role
      const userRole = await this.crudService.findOne<UserRole>(
        this.userRoleModel,
        {
          where: { userId },
          include: [{ model: Role }],
        },
      );

      if (!userRole?.role) {
        return false;
      }

      // Compare role IDs
      return userRole.roleId === superAdminRole.id;
    } catch (error) {
      console.error(`Error checking if user ${userId} is Super Admin:`, error);
      return false;
    }
  }

  /**
   * Get user's role ID
   */
  async getUserRoleId(userId: number): Promise<number | null> {
    try {
      const userRole = await this.crudService.findOne<UserRole>(
        this.userRoleModel,
        {
          where: { userId },
        },
      );

      return userRole?.roleId || null;
    } catch (error) {
      console.error(`Error getting role ID for user ${userId}:`, error);
      return null;
    }
  }

  async getUserRoleScope(userId: number): Promise<ROLE_SCOPE_ENUM | null> {
    const userRole = await this.getUserRoleId(userId);
    const role = await this.crudService.findOne<Role>(this.roleModel, {
      where: { id: userRole },
    });
    return role?.scope || null;
  }

  /**
   * Clear Super Admin role cache (use when roles are updated)
   */
  async clearSuperAdminCache(): Promise<void> {
    await this.redisCache.del(this.SUPER_ADMIN_CACHE_KEY);
  }

  async create(data: Partial<Role>, user?: RequestUserObjectI) {
    // if user role scope is company than get the company id from the user model and add company id to data
    const userRoleScope = await this.getUserRoleScope(user.id);

    if (userRoleScope === ROLE_SCOPE_ENUM.COMPANY) {
      const userData = await this.userModel.findOne({
        where: { id: user.id },
      });
      data.companyId = userData.companyId;
    }

    const role = (await this.crudService.create(this.roleModel, data)) as Role;

    // Clear relevant caches
    await this.clearRoleCaches(role.name, role.scope);

    return role;
  }

  async createRoleWithoutCompanyId(
    data: Partial<Role>,
    transaction?: any,
  ): Promise<Partial<Role>> {
    const role = (await this.crudService.create(
      this.roleModel,
      data,
      transaction,
    )) as Partial<Role>;
    return role;
  }

  async findAllByUserPaginated(
    userId: number,
    dto: UserFindAllRolesDto,
  ): Promise<PaginatedResult<Partial<Role>>> {
    const company = await this.companyService.findByUserId(userId);

    return this.crudService.paginateWithQuery<Role>(this.roleModel, {
      page: dto.page,
      limit: dto.limit,
      where: {
        companyId: company.id,
        scope: ROLE_SCOPE_ENUM.COMPANY,
      },
      order: [['createdAt', 'DESC']],
    });
  }

  async findAllCompanyRoles(userId: number, dto: FindAllRolesDto) {
    const company = await this.companyService.findByUserId(userId);

    return this.crudService.findAll(this.roleModel, {
      where: {
        companyId: company.id,
        scope: ROLE_SCOPE_ENUM.COMPANY,
      },
    });
  }

  async findAllPlatformRoles(dto: FindAllRolesDto) {
    return this.crudService.paginateWithQuery<Role>(this.roleModel, {
      page: dto.page,
      limit: dto.limit,
      where: {
        scope: ROLE_SCOPE_ENUM.PLATFORM,
      },
      order: [['createdAt', 'DESC']],
    });
  }

  async findAllCompanyRolesPaginated(
    user: RequestUserObjectI,
    dto: FindAllRolesDto,
  ): Promise<PaginatedResult<Partial<Role>>> {
    const company = await this.companyService.findByUserId(user.id);

    const roles = await this.crudService.paginateWithQuery<Role>(
      this.roleModel,
      {
        page: dto.page,
        limit: dto.limit,
        where: {
          companyId: company.id,
          scope: ROLE_SCOPE_ENUM.COMPANY,
        },
      },
    );

    return roles;
  }

  // find all platform roles paginated
  async findAllPlatformRolesPaginated(
    dto: FindAllRolesDto,
  ): Promise<PaginatedResult<Partial<Role>>> {
    return this.crudService.paginateWithQuery<Role>(this.roleModel, {
      page: dto.page,
      limit: dto.limit,
      where: {
        scope: ROLE_SCOPE_ENUM.PLATFORM,
      },
      order: [['createdAt', 'DESC']],
    });
  }

  async findById(id: number, userId?: number) {
    const where: any = { id };

    // If user is provided (company user), filter by their company
    if (userId) {
      const isSuperAdmin = await this.isUserSuperAdmin(userId);

      if (!isSuperAdmin) {
        // Company users can only access roles in their company
        const company = await this.companyService.findByUserId(userId);
        where.companyId = company.id;
      }
    }

    const data = await this.crudService.findOne(this.roleModel, {
      where,
      include: [
        {
          model: Permission,
          include: [{ model: PlatformSection }],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    return data;
  }

  async findRoleByName(name: string): Promise<Partial<Role>> {
    const cacheKey = `role-by-name:${name}`;
    const cacheTtl = 15 * 60; // 15 minutes in seconds

    try {
      // Try to get from Redis cache first
      const cachedRole = await this.redisCache.get<Partial<Role>>(cacheKey);
      if (cachedRole) {
        return cachedRole;
      }

      // Fetch from database
      const role = await this.crudService.findOne<Role>(this.roleModel, {
        where: { name },
      });

      // Cache the result
      if (role) {
        await this.redisCache.set(cacheKey, role, cacheTtl);
      }

      return role;
    } catch (error) {
      console.error(`Error fetching role by name ${name}:`, error);
      // Fallback to database query
      return this.crudService.findOne<Role>(this.roleModel, {
        where: { name },
      });
    }
  }

  async setPermissions(
    roleId: number,
    dto: SetPermissionsDto,
    user?: RequestUserObjectI,
  ) {
    const where: any = { id: roleId };

    if (user?.role.scope === ROLE_SCOPE_ENUM.COMPANY) {
      const company = await this.companyService.findByUserId(user.id);
      where.companyId = company.id;
      where.scope = ROLE_SCOPE_ENUM.COMPANY;
    }

    // Verify the role exists and user has access to it
    const role = await this.crudService.findOne(this.roleModel, { where });
    if (!role) {
      throw new Error('Role not found or access denied');
    }

    // Find existing permission or create new one, then update
    const [permission, created] = await this.permissionModel.findOrCreate({
      where: {
        roleId,
        platformSectionId: dto.platformSectionId,
      },
      defaults: {
        roleId,
        platformSectionId: dto.platformSectionId,
        level: dto.level,
        canView: dto.canView || false,
        canCreate: dto.canCreate || false,
        canEdit: dto.canEdit || false,
        canDelete: dto.canDelete || false,
      },
    });

    // If permission already existed, update it
    if (!created) {
      await permission.update({
        level: dto.level,
        canView: dto.canView || false,
        canCreate: dto.canCreate || false,
        canEdit: dto.canEdit || false,
        canDelete: dto.canDelete || false,
      });
    }

    // Return the updated role with permissions using findById which now handles associations properly
    return this.findById(roleId, user.id);
  }

  async update(id: number, data: Partial<Role>, user?: RequestUserObjectI) {
    const where: any = { id };

    // If user is provided (company user), filter by their company
    if (user) {
      const isSuperAdmin = await this.isUserSuperAdmin(user.id);

      if (!isSuperAdmin) {
        // Company users can only update roles in their company
        const company = await this.companyService.findByUserId(user.id);
        where.companyId = company.id;
        where.scope = ROLE_SCOPE_ENUM.COMPANY;
      }
    }

    // Get the role before updating to clear caches
    const existingRole = (await this.crudService.findOne(this.roleModel, {
      where,
    })) as Role;

    const updatedRole = await this.crudService.update(this.roleModel, data, {
      where,
    });

    // Clear relevant caches
    if (existingRole) {
      await this.clearRoleCaches(existingRole.name, existingRole.scope);
    }
    if (data.name && data.name !== existingRole?.name) {
      await this.clearRoleCaches(data.name, data.scope || existingRole?.scope);
    }

    return updatedRole;
  }

  async delete(id: number, user?: RequestUserObjectI) {
    const where: any = { id };

    // If user is provided (company user), filter by their company
    if (user) {
      const isSuperAdmin = await this.isUserSuperAdmin(user.id);

      if (!isSuperAdmin) {
        // Company users can only delete roles in their company
        const company = await this.companyService.findByUserId(user.id);
        where.companyId = company.id;
        where.scope = ROLE_SCOPE_ENUM.COMPANY;
      }
    }

    // Get the role before deleting to clear caches
    const existingRole = (await this.crudService.findOne(this.roleModel, {
      where,
    })) as Role;

    const result = await this.crudService.delete(this.roleModel, { where });

    // Clear relevant caches
    if (existingRole) {
      await this.clearRoleCaches(existingRole.name, existingRole.scope);
    }

    return result;
  }

  /**
   * Clear role-related caches
   */
  private async clearRoleCaches(
    name: string,
    scope: ROLE_SCOPE_ENUM,
  ): Promise<void> {
    try {
      // Clear role by name cache
      await this.redisCache.del(`role-by-name:${name}`);

      // Clear role by name and scope cache
      await this.redisCache.del(`role-by-name-scope:${name}:${scope}`);

      // Clear super admin cache if this is a super admin role
      if (name === ROLES_ENUM.PLATFORM_SUPER_ADMIN) {
        await this.clearSuperAdminCache();
      }
    } catch (error) {
      console.error('Error clearing role caches:', error);
    }
  }

  async getRoleByNameAndScope(name: string, scope: ROLE_SCOPE_ENUM) {
    const cacheKey = `role-by-name-scope:${name}:${scope}`;
    const cacheTtl = 15 * 60; // 15 minutes in seconds

    try {
      // Try to get from Redis cache first
      const cachedRole = await this.redisCache.get<Partial<Role>>(cacheKey);
      if (cachedRole) {
        return cachedRole;
      }

      // Fetch from database
      const role = await this.crudService.findOne<Role>(this.roleModel, {
        where: { name, scope: scope },
      });

      // Cache the result
      if (role) {
        await this.redisCache.set(cacheKey, role, cacheTtl);
      }

      return role;
    } catch (error) {
      console.error(
        `Error fetching role by name ${name} and scope ${scope}:`,
        error,
      );
      // Fallback to database query
      return this.crudService.findOne<Role>(this.roleModel, {
        where: { name, scope: scope },
      });
    }
  }

  /**
   * Give permissions to company super admin when creating a company
   * This function assigns all company-level permissions to the company super admin role
   */
  async giveCompanySuperAdminPermissionsByCompanyId(
    companyId: number,
  ): Promise<void> {
    try {
      // First, try to find the company super admin role for this company
      let companySuperAdminRole = await this.crudService.findOne<Role>(
        this.roleModel,
        {
          where: {
            name: ROLES_ENUM.COMPANY_SUPER_ADMIN,
            scope: ROLE_SCOPE_ENUM.COMPANY,
            companyId: companyId,
          },
        },
      );

      // If not found, try to find any company super admin role without companyId and update it
      if (!companySuperAdminRole) {
        companySuperAdminRole = await this.crudService.findOne<Role>(
          this.roleModel,
          {
            where: {
              name: ROLES_ENUM.COMPANY_SUPER_ADMIN,
              scope: ROLE_SCOPE_ENUM.COMPANY,
            },
          },
        );

        if (companySuperAdminRole) {
          // Update the role with the companyId
          await this.crudService.update(
            this.roleModel,
            { companyId: companyId },
            { where: { id: companySuperAdminRole.id } },
          );
          console.log(
            'here is *',
            'Updated company super admin role with companyId:',
            companyId,
          );
        }
      }

      // If still not found, create the role
      if (!companySuperAdminRole) {
        companySuperAdminRole = await this.crudService.create<Role>(
          this.roleModel,
          {
            name: ROLES_ENUM.COMPANY_SUPER_ADMIN,
            description: 'Has all permissions for the company',
            scope: ROLE_SCOPE_ENUM.COMPANY,
            companyId: companyId,
          },
        );
        console.log(
          'here is *',
          'Created new company super admin role for company:',
          companyId,
        );
      }

      // Get all platform sections that are available for company level
      const platformSections = await this.crudService.findAll<PlatformSection>(
        this.platformSectionModel,
        {
          where: {
            scope: {
              [Op.or]: [
                PlatformSectionScopeEnum.BOTH,
                PlatformSectionScopeEnum.COMPANY_ONLY,
              ],
            },
          },
        },
      );

      console.log(
        'here is *',
        'Found platform sections:',
        platformSections.length,
      );

      // Create permissions for each platform section
      for (const section of platformSections) {
        await this.permissionModel.findOrCreate({
          where: {
            roleId: companySuperAdminRole.id,
            platformSectionId: section.id,
          },
          defaults: {
            roleId: companySuperAdminRole.id,
            platformSectionId: section.id,
            level: PermissionLevelEnum.COMPANY,
            canView: true,
            canCreate: true,
            canEdit: true,
            canDelete: true,
          },
        });
      }

      console.log(
        'here is *',
        'Successfully assigned permissions to company super admin',
      );
    } catch (error) {
      console.error(
        'Error assigning permissions to company super admin:',
        error,
      );
      throw error;
    }
  }

  async giveCompanySuperAdminPermissionsByRoleId(
    roleId: number,
    transaction?: any,
  ): Promise<Partial<Permission>[]> {
    const companySections =
      await this.platformSectionsService.findOnlyCompanySections();

    const permissions: Partial<Permission>[] = [];

    for (const section of companySections) {
      permissions.push(
        await this.crudService.create<Permission>(
          this.permissionModel,
          {
            roleId: roleId,
            platformSectionId: section.id,
            level: PermissionLevelEnum.COMPANY,
            canView: true,
            canCreate: true,
            canEdit: true,
            canDelete: true,
          },
          transaction,
        ),
      );
    }

    return permissions;
  }
}

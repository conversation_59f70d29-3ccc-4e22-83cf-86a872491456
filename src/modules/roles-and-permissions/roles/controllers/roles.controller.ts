import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import {
  CanCreate,
  CanDelete,
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { FindAllRolesDto } from '../dto/find.all.dto';
import { SetPermissionsDto } from '../dto/set-permissions.dto';
import { UserFindAllRolesDto } from '../dto/user.find.all.dto';
import { RolesService } from '../roles.service';

@Controller('roles')
@UseGuards(PermissionGuard)
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.SETTINGS)
  create(@Body() dto: Partial<Role>, @GetUser() user: RequestUserObjectI) {
    return this.rolesService.create(dto, user);
  }

  @Get()
  @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  findAll(@GetUser('id') userId: number, @Query() dto: FindAllRolesDto) {
    return this.rolesService.findAllCompanyRoles(userId, dto);
  }

  @Get('paginated')
  @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  findAllPaginated(
    @GetUser('id') userId: number,
    @Query() dto: UserFindAllRolesDto,
  ) {
    return this.rolesService.findAllByUserPaginated(userId, dto);
  }

  @Get(':id')
  @CanView(PLATFORM_SECTION_ENUM.SETTINGS)
  findById(
    @Param('id', ParseIntPipe) id: number,
    @GetUser('id') userId: number,
  ) {
    return this.rolesService.findById(id, userId);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.SETTINGS)
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: Partial<Role>,
    @GetUser() user: RequestUserObjectI,
  ) {
    return this.rolesService.update(id, dto, user);
  }

  @Put(':id/permissions')
  @CanEdit(PLATFORM_SECTION_ENUM.SETTINGS)
  setPermissions(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: SetPermissionsDto,
    @GetUser() user: RequestUserObjectI,
  ) {
    return this.rolesService.setPermissions(id, dto, user);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.SETTINGS)
  delete(
    @Param('id', ParseIntPipe) id: number,
    @GetUser() user: RequestUserObjectI,
  ) {
    return this.rolesService.delete(id, user);
  }
}

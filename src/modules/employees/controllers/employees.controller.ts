// import {
//   Controller,
//   Get,
//   Post,
//   Body,
//   Patch,
//   Param,
//   Delete,
//   UseGuards,
// } from '@nestjs/common';
// import { EmployeesService } from '../services/employees.service';
// import { User } from '../../../models/users-models/user.model';
// import { PermissionGuard } from '../../auth/guards/permission.guard';

// @Controller('employees')
// @UseGuards(PermissionGuard) // Apply to all routes in this controller
// export class EmployeesController {
//   constructor(private readonly employeesService: EmployeesService) {}

//   @Post() // Auto-detects: employees/canCreate
//   async create(@Body() createEmployeeDto: any): Promise<User> {
//     return this.employeesService.create(createEmployeeDto);
//   }

//   @Get() // Auto-detects: employees/canView
//   async findAll(): Promise<User[]> {
//     return this.employeesService.findAll();
//   }

//   @Get(':id') // Auto-detects: employees/canView
//   async findOne(@Param('id') id: number): Promise<User> {
//     return this.employeesService.findOne(id);
//   }

//   @Patch(':id') // Auto-detects: employees/canEdit
//   async update(
//     @Param('id') id: number,
//     @Body() updateEmployeeDto: any,
//   ): Promise<User> {
//     return this.employeesService.update(id, updateEmployeeDto);
//   }

//   @Delete(':id') // Auto-detects: employees/canDelete
//   async remove(@Param('id') id: number): Promise<void> {
//     return this.employeesService.remove(id);
//   }
// }

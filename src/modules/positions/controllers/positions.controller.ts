import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CreatePositionDto } from '../dto/create-position.dto';
import { UpdatePositionDto } from '../dto/update-position.dto';
import { PositionsService } from '../services/positions.service';

@UseGuards(JwtAuthGuard)
@Controller('positions')
export class PositionsController {
  constructor(private readonly positionsService: PositionsService) {}

  @Post()
  create(
    @Body() createPositionDto: CreatePositionDto,
    @GetUser('companyId') companyId: number,
  ) {
    return this.positionsService.create(createPositionDto, companyId);
  }

  @Get()
  findAll(@GetUser('companyId') companyId: number) {
    return this.positionsService.findAll(companyId);
  }

  @Get('department/:departmentId')
  findByDepartment(@Param('departmentId', ParseIntPipe) departmentId: number) {
    return this.positionsService.findByDepartment(departmentId);
  }

  @Put(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePositionDto: UpdatePositionDto,
    @GetUser('companyId') companyId: number,
  ) {
    return this.positionsService.update(id, updatePositionDto, companyId);
  }

  @Delete(':id')
  remove(
    @Param('id', ParseIntPipe) id: number,
    @GetUser('companyId') companyId: number,
  ) {
    return this.positionsService.remove(id, companyId);
  }
}

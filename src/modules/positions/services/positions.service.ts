import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Company } from '../../../models/company.model';
import { Department } from '../../../models/department.model';
import { Position } from '../../../models/position.model';
import { CreatePositionDto } from '../dto/create-position.dto';
import { UpdatePositionDto } from '../dto/update-position.dto';

@Injectable()
export class PositionsService {
  constructor(
    @InjectModel(Position)
    private readonly positionModel: typeof Position,
  ) {}

  async create(
    createPositionDto: CreatePositionDto,
    companyId: number,
  ): Promise<Position> {
    const positionData = {
      ...createPositionDto,
      companyId,
    };

    return await this.positionModel.create(positionData);
  }

  async findAll(companyId: number): Promise<Position[]> {
    return await this.positionModel.findAll({
      where: { companyId },
      include: [
        {
          model: Department,
          attributes: ['id', 'name'],
        },
        {
          model: Company,
          attributes: ['id', 'name'],
        },
      ],
      order: [['title', 'ASC']],
    });
  }

  async findOne(id: number, companyId: number): Promise<Position> {
    const position = await this.positionModel.findOne({
      where: {
        id,
        companyId,
      },
      include: [
        {
          model: Department,
          attributes: ['id', 'name'],
        },
        {
          model: Company,
          attributes: ['id', 'name'],
        },
      ],
    });

    if (!position) {
      throw new NotFoundException(`Position with ID ${id} not found`);
    }

    return position;
  }

  async update(
    id: number,
    updatePositionDto: UpdatePositionDto,
    companyId: number,
  ): Promise<Position> {
    const position = await this.findOne(id, companyId);

    await position.update(updatePositionDto);
    return position;
  }

  async remove(id: number, companyId: number): Promise<void> {
    const position = await this.findOne(id, companyId);
    await position.destroy();
  }

  async findByDepartment(departmentId: number): Promise<Position[]> {
    return await this.positionModel.findAll({
      where: {
        departmentId,
        isActive: true,
      },
      include: [
        {
          model: Department,
          attributes: ['id', 'name'],
        },
      ],
      order: [['title', 'ASC']],
    });
  }
}

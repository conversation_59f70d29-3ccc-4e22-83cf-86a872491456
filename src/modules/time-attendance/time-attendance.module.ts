import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { User } from 'src/models/users-models/user.model';
import { TimeAttendance } from '../../models/time-attendance.model';
import { AdminTimeAttendanceController, CompanyTimeAttendanceController, TimeAttendanceController } from './controllers';
import { } from './controllers/admin-time-attendance.controller';
import { EmployeeTimeAttendanceService } from './services/time-attendance.service';
@Module({
  imports: [
    SequelizeModule.forFeature([TimeAttendance, User, EmploymentDetails]),
  ],
  controllers: [TimeAttendanceController, AdminTimeAttendanceController, CompanyTimeAttendanceController],
  providers: [EmployeeTimeAttendanceService],
  exports: [EmployeeTimeAttendanceService],
})
export class TimeAttendanceModule {}

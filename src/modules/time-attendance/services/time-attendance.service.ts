import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, WhereOptions } from 'sequelize';
import { CrudHelperService } from 'src/common/crud-helper/crud-helper.service';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { User } from 'src/models/users-models/user.model';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { getDateRange, getDaysInMonth } from 'src/utils/date.util';
import {
  DUBAI_TZ,
  getLocalDateString,
  getMondayOfCurrentWeek,
  getNowInTimezone,
  getStartOfDay,
} from 'src/utils/date.utils';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { buildAttendanceDaySummary } from '../utils/attendance-day.builder';
import {
  buildUserAttendanceSummary,
  getCompanySettings,
  groupAttendancesByUser,
} from '../utils/time-attendance.helpers';
import {
  calculateLateAndAbsentDays,
  calculateOvertime,
  calculateWorkedHours,
} from '../utils/time.attendence.utils';

@Injectable()
export class EmployeeTimeAttendanceService {
  constructor(
    @InjectModel(TimeAttendance)
    private readonly timeAttendanceModel: typeof TimeAttendance,
    private readonly crudService: CrudHelperService,
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}

  async clockIn(user: RequestUserObjectI): Promise<Partial<TimeAttendance>> {
    try {
      // Fetch user with timezone
      const dbUser = await this.userModel.findOne({ where: { id: user.id } });
      const timeZone = dbUser?.timeZone || DUBAI_TZ;
      // Get current time in user's timezone, then convert to UTC for storage
      const nowLocal = getNowInTimezone(timeZone);
      const nowUTC = nowLocal; // getNowInTimezone returns a Date in the correct timezone, so store as is or use .toISOString() if needed
      // Set date as YYYY-MM-DD in user's timezone
      const dateStr = getLocalDateString(nowLocal, timeZone);

      const lastAttendance = await this.crudService.findOne<TimeAttendance>(
        TimeAttendance,
        {
          where: { userId: user.id, companyId: user.companyId },
          order: [['createdAt', 'DESC']],
        },
      );

      if (lastAttendance && !lastAttendance.clockOutTime) {
        throw new BadRequestException(
          'You have already checked in. Please check out before clocking in again.',
        );
      }

      const attendanceData = {
        userId: user.id,
        companyId: user.companyId,
        clockInTime: nowUTC,
        date: dateStr,
      };

      const checkedIn = await this.crudService.create<TimeAttendance>(
        TimeAttendance,
        attendanceData,
      );

      console.log('here is * Stored attendance:', checkedIn);
      return checkedIn;
    } catch (error) {
      console.error('here is * Error during clock-in:', error);
      throw error;
    }
  }

  async clockOut(user: RequestUserObjectI): Promise<TimeAttendance> {
    try {
      // Fetch user with timezone
      const dbUser = await this.userModel.findOne({ where: { id: user.id } });
      const timeZone = dbUser?.timeZone || DUBAI_TZ;
      // Get current time in user's timezone, then convert to UTC for storage
      const nowLocal = getNowInTimezone(timeZone);
      const nowUTC = nowLocal; // getNowInTimezone returns a Date in the correct timezone, so store as is or use .toISOString() if needed
      // Set date as YYYY-MM-DD in user's timezone
      const dateStr = getLocalDateString(nowLocal, timeZone);

      const lastClockIn = await this.timeAttendanceModel.findOne({
        where: {
          userId: user.id,
          companyId: user.companyId,
          clockOutTime: null,
        },
        order: [['date', 'DESC']],
      });

      if (!lastClockIn) {
        throw new NotFoundException(
          'No active clock-in record found to clock out.',
        );
      }

      lastClockIn.clockOutTime = nowUTC;
      await lastClockIn.save();
      return lastClockIn;
    } catch (error) {
      console.error('here is * Error storing attendance:', error);
      throw error;
    }
  }

  async findByDate(userId: number, date: string): Promise<TimeAttendance[]> {
    const result = await this.timeAttendanceModel.findAll({
      where: { date, userId },
      include: [
        { model: User, attributes: ['id', 'firstName', 'lastName', 'email'] },
      ],
      order: [['clockInTime', 'DESC']],
    });
    return result;
  }

  async generateMonthlyReport({
    month,
    year,
    userId,
  }: {
    month: number;
    year: number;
    userId: number;
  }) {
    // Fetch user with timezone
    const dbUser = await this.userModel.findOne({ where: { id: userId } });
    const timeZone = dbUser?.timeZone || DUBAI_TZ;
    const { startDate, endDate } = getDateRange(month, year);

    const attendances = await this.timeAttendanceModel.findAll({
      where: {
        userId,
        date: {
          [Op.between]: [startDate, endDate],
        },
      },
    });

    const daysInMonth = getDaysInMonth(month, year);
    const today = getNowInTimezone(timeZone);

    const lateAndAbsentStats = calculateLateAndAbsentDays(
      attendances,
      daysInMonth,
      month,
      year,
      today,
      timeZone,
    );

    const workedStats = calculateWorkedHours(
      attendances,
      daysInMonth,
      timeZone,
    );
    const overtimeStats = calculateOvertime(attendances, timeZone);

    return {
      monthStats: lateAndAbsentStats,
      workedHours: workedStats,
      overTime: overtimeStats,
    };
  }

  private async fetchTimeAttendanceRecords(userIds: number[], date: string) {
    return this.timeAttendanceModel.findAll({
      where: {
        userId: { [Op.in]: userIds },
        date,
      },
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email', 'profileImage'],
        },
      ],
      attributes: [
        'id',
        'userId',
        'clockInTime',
        'clockOutTime',
        'date',
        'notes',
        'createdAt',
        'updatedAt',
      ],
    });
  }

  async getCompanyEmployeesTimeAttendance(
    companyId: number,
    page: number = 1,
    limit: number = 10,
    q?: string,
  ) {
    const offset = (page - 1) * limit;

    // Build where clause for search
    let whereClause: any = {
      companyId,
    };

    let userIdsWithEmployeeId: number[] = [];

    if (q) {
      // First, check if the search term matches any employee IDs
      const usersWithEmployeeId = await this.userModel.findAll({
        where: {
          companyId,
        },
        include: [
          {
            model: EmploymentDetails,
            where: {
              employeeId: { [Op.iLike]: `%${q}%` },
            },
            required: true,
          },
        ],
        attributes: ['id'],
      });

      userIdsWithEmployeeId = usersWithEmployeeId.map((u) => u.id);

      // Build search conditions for name and email
      whereClause = {
        companyId,
        [Op.or]: [
          { firstName: { [Op.iLike]: `%${q}%` } },
          { lastName: { [Op.iLike]: `%${q}%` } },
          { email: { [Op.iLike]: `%${q}%` } },
          ...(userIdsWithEmployeeId.length > 0
            ? [{ id: { [Op.in]: userIdsWithEmployeeId } }]
            : []),
        ],
      };
    }

    // Get total count for pagination with search filter
    const totalCount = await this.userModel.count({
      where: whereClause,
    });

    const companyEmployees = await this.userModel.findAll({
      where: whereClause,
      attributes: ['id', 'firstName', 'lastName', 'email', 'profileImage'],
      include: [
        {
          model: EmploymentDetails,
          required: false,
        },
      ],
      offset,
      limit,
    });

    const companyEmployeesIds = companyEmployees.map((e) => e.id);
    const today = getNowInTimezone(DUBAI_TZ).toISOString().split('T')[0];

    const { workingDays, holidays } = await getCompanySettings(companyId);
    const timeAttendances = await this.fetchTimeAttendanceRecords(
      companyEmployeesIds,
      today,
    );
    const attendanceByUser = groupAttendancesByUser(timeAttendances);

    const todayDate = getNowInTimezone(DUBAI_TZ);
    const result = companyEmployees.map((employee) => {
      const summary = buildUserAttendanceSummary(
        employee,
        todayDate,
        attendanceByUser[employee.id] || [],
        workingDays,
        holidays,
      );
      return {
        user: {
          id: employee.id,
          firstName: employee.firstName,
          lastName: employee.lastName,
          email: employee.email,
          profileImage: employee.profileImage,
          employeeCode: employee.employmentDetails?.[0]?.employeeId || null,
        },
        ...summary,
      };
    });

    // Calculate pagination meta
    const lastPage = Math.ceil(totalCount / limit);

    return {
      success: true,
      message: 'Request successful',
      meta: {
        total: totalCount,
        page: page,
        lastPage: lastPage,
        limit: limit,
        totalRecords: totalCount,
      },
      data: result,
    };
  }

  findOne(id: number): Promise<Partial<TimeAttendance>> {
    return this.crudService.findOne<TimeAttendance>(TimeAttendance, {
      where: { id },
      include: { all: true },
    });
  }

  async update(
    id: number,
    dto: UpdateTimeAttendanceDto,
    companyId: number,
  ): Promise<Partial<TimeAttendance>> {
    // TODO: if user is updating record of thieri own company
    await this.findOne(id); // ensure existence
    await this.crudService.update(TimeAttendance, dto, {
      where: { id, companyId },
    });
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    await this.findOne(id);
    await this.crudService.delete(TimeAttendance, { where: { id } });
  }

  async findAllUserIdsForDate(date: Date): Promise<number[]> {
    const records = await this.timeAttendanceModel.findAll({
      where: { date },
      attributes: ['userId'],
    });
    return records.map((r) => r.userId);
  }

  async findAllAttendanceRecordsToAutoClockOut(): Promise<TimeAttendance[]> {
    return this.timeAttendanceModel.findAll({
      where: {
        clockOutTime: null,
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName', 'timeZone'],
        },
      ],
    });
  }

  async findAllAttendanceRecordsForDateRange(
    start: Date,
    end: Date,
  ): Promise<TimeAttendance[]> {
    return this.timeAttendanceModel.findAll({
      where: {
        date: { [Op.between]: [start, end] },
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'email', 'firstName', 'lastName'],
        },
      ],
    });
  }

  async deleteAttendanceRecordsOlderThan(cutoff: Date): Promise<number> {
    return this.timeAttendanceModel.destroy({
      where: {
        date: { [Op.lt]: cutoff },
      },
    });
  }

  async getAttendanceSummary(
    { userId, companyId }: { userId: number; companyId?: number },
    start?: string,
    end?: string,
  ) {
    const whereCondition: WhereOptions<User> = {
      id: userId,
    };

    if (companyId !== undefined) {
      whereCondition.companyId = companyId;
    }

    const userInfo = await this.crudService.findOne<User>(User, {
      where: whereCondition,
      include: [
        {
          model: EmploymentDetails,
          required: false,
        },
      ],
    });

    if (!userInfo) {
      throw new NotFoundException(`User not found at id: ${userId}`);
    }

    const timeZone = userInfo.timeZone || DUBAI_TZ;
    const today = getNowInTimezone(timeZone);
    const endDate = end ? new Date(end) : today;
    const startDate = start ? new Date(start) : getMondayOfCurrentWeek(today);

    const { workingDays, holidays } = await getCompanySettings(
      userInfo.companyId,
    );

    // Fetch attendance records for the entire date range
    const timeAttendances = await this.timeAttendanceModel.findAll({
      where: {
        userId,
        date: {
          [Op.between]: [startDate, endDate],
        },
      },
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email', 'profileImage'],
        },
      ],
      attributes: [
        'id',
        'userId',
        'clockInTime',
        'clockOutTime',
        'date',
        'notes',
        'createdAt',
        'updatedAt',
      ],
    });

    // Group attendance records by date in user's timezone
    const attendanceByDate = timeAttendances.reduce(
      (acc, record) => {
        // Convert UTC time to user's local time for grouping
        const localClockIn = record.clockInTime
          ? new Date(record.clockInTime.toLocaleString('en-US', { timeZone }))
          : null;

        // Use clock-in date as the key for grouping
        const dateStr = localClockIn
          ? getLocalDateString(localClockIn, timeZone)
          : getLocalDateString(new Date(record.date), timeZone);

        if (!acc[dateStr]) {
          acc[dateStr] = [];
        }

        acc[dateStr].push({
          id: record.id,
          clockInTime: record.clockInTime?.toISOString() || null,
          clockOutTime: record.clockOutTime?.toISOString() || null,
          notes: record.notes || null,
        });

        return acc;
      },
      {} as Record<string, any[]>,
    );

    const days = [];
    const seenDates = new Set<string>();
    for (
      let d = new Date(startDate);
      d <= endDate;
      d.setDate(d.getDate() + 1)
    ) {
      const localDate = getStartOfDay(d, timeZone);
      const dateStr = getLocalDateString(localDate, timeZone);
      if (seenDates.has(dateStr)) continue; // Skip duplicate days
      seenDates.add(dateStr);
      const summary = buildAttendanceDaySummary(
        localDate,
        attendanceByDate[dateStr] || [],
        workingDays,
        holidays,
        timeZone,
      );
      days.push(summary);
    }

    return {
      success: true,
      message: 'Request successful',
      data: {
        user: {
          id: userInfo.id,
          name: `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim(),
          profileImage: userInfo.profileImage,
          employeeCode: userInfo.employmentDetails?.[0]?.employeeId || null,
        },
        days,
      },
    };
  }

  /**
   * Get the latest time attendance record for a specific employee (by userId)
   */
  async getLatestAttendanceByUserId(
    companyId: number,
    userId: number,
  ): Promise<TimeAttendance | null> {
    return this.timeAttendanceModel.findOne({
      where: { userId, companyId },
      include: [
        {
          model: User,
          attributes: ['id', 'firstName', 'lastName', 'email', 'profileImage'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }
}

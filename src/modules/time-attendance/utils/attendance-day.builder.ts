import {
  AttendanceActivity,
  AttendanceDaySummary,
} from '../dto/attendance-summary.dto';
import {
  WORK_START_HOUR,
  WORK_START_MINUTE,
  STANDARD_WORK_HOURS,
  PRESENT_LABEL,
  ABSENT_LABEL,
  WEEKEND_LABEL,
  HOLIDAY_LABEL,
  TIME_FORMAT,
  getWorkStartTime,
} from 'src/constants/attendance.constants';
import {
  isWeekend,
  isHoliday,
  formatDuration,
  getStartOfDay,
  getEndOfDay,
  getLocalDateString,
} from 'src/utils/date.utils';

function getLocalTime(
  utcTime: string | null,
  timeZone: string,
  baseDate: Date,
): Date | null {
  if (!utcTime) return null;
  const localTime = new Date(
    new Date(utcTime).toLocaleString('en-US', { timeZone }),
  );
  // Keep the time but set the date to match the base date
  const result = new Date(baseDate);
  result.setHours(
    localTime.getHours(),
    localTime.getMinutes(),
    localTime.getSeconds(),
    localTime.getMilliseconds(),
  );
  return result;
}

export function buildAttendanceDaySummary(
  date: Date,
  activities: AttendanceActivity[],
  workingDays: string[],
  holidays: string[],
  timeZone: string,
): AttendanceDaySummary {
  // Convert date to user's local date and ensure it's set to midnight
  const localDate = getStartOfDay(date, timeZone);

  // Get end of day boundary
  const endOfDay = getEndOfDay(date, timeZone);

  // Format date strings in user's timezone
  const dateStr = getLocalDateString(localDate, timeZone); // YYYY-MM-DD
  const dayOfWeek = localDate.toLocaleDateString('en-US', {
    weekday: 'short',
    timeZone,
  });

  const weekend = isWeekend(localDate, workingDays, timeZone);
  const holiday = isHoliday(localDate, holidays, timeZone);
  let status = ABSENT_LABEL;
  let checkIn: string | null = null;
  let checkOut: string | null = null;
  let workedHours: string | null = null;
  let overtime: string | null = null;
  let lateBy: string | null = null;
  const dayType = holiday ? HOLIDAY_LABEL : weekend ? WEEKEND_LABEL : 'weekday';

  if (holiday) {
    status = ABSENT_LABEL;
  } else if (weekend) {
    status = ABSENT_LABEL;
  } else if (activities.length > 0) {
    status = PRESENT_LABEL;

    // Convert all times to local time within the day's boundaries
    const clockInTimes = activities
      .filter((a) => a.clockInTime)
      .map((a) => getLocalTime(a.clockInTime, timeZone, localDate))
      .filter((time): time is Date => time !== null);

    const clockOutTimes = activities
      .filter((a) => a.clockOutTime)
      .map((a) => {
        const localTime = getLocalTime(a.clockOutTime, timeZone, localDate);
        if (!localTime) return null;
        // If clock-out is past midnight, cap it at end of day
        return localTime > endOfDay ? endOfDay : localTime;
      })
      .filter((time): time is Date => time !== null);

    if (clockInTimes.length > 0) {
      const earliestIn = new Date(
        Math.min(...clockInTimes.map((d) => d.getTime())),
      );
      checkIn = earliestIn.toLocaleTimeString('en-US', {
        ...TIME_FORMAT,
        timeZone,
      });

      // Get work start time in user's timezone
      const workStart = getWorkStartTime(localDate, timeZone);

      // Calculate late minutes if clock-in is after work start time
      if (earliestIn > workStart) {
        const lateMinutes = Math.floor(
          (earliestIn.getTime() - workStart.getTime()) / (1000 * 60),
        );
        lateBy = formatDuration(lateMinutes);
      }
    }

    if (clockOutTimes.length > 0) {
      const latestOut = new Date(
        Math.max(...clockOutTimes.map((d) => d.getTime())),
      );
      checkOut = latestOut.toLocaleTimeString('en-US', {
        ...TIME_FORMAT,
        timeZone,
      });
    }

    // Calculate total worked hours within this day's boundaries
    let totalMinutes = 0;
    for (const activity of activities) {
      const clockIn = getLocalTime(activity.clockInTime, timeZone, localDate);
      const clockOut = activity.clockOutTime
        ? getLocalTime(activity.clockOutTime, timeZone, localDate)
        : null;

      if (clockIn && clockOut) {
        // Ensure times are within day boundaries
        const effectiveStart = clockIn < localDate ? localDate : clockIn;
        const effectiveEnd = clockOut > endOfDay ? endOfDay : clockOut;

        if (effectiveEnd > effectiveStart) {
          const sessionMinutes = Math.floor(
            (effectiveEnd.getTime() - effectiveStart.getTime()) / (1000 * 60),
          );
          totalMinutes += sessionMinutes;
        }
      }
    }

    workedHours = formatDuration(totalMinutes);

    // Calculate overtime (anything over STANDARD_WORK_HOURS)
    if (totalMinutes > STANDARD_WORK_HOURS * 60) {
      const otMinutes = totalMinutes - STANDARD_WORK_HOURS * 60;
      overtime = formatDuration(otMinutes);
    }
  }

  return {
    date: dateStr,
    day: dayOfWeek,
    attendanceActivities: activities,
    checkIn,
    checkOut,
    workedHours,
    overtime,
    status,
    lateBy,
    dayType,
    isHoliday: holiday,
  };
}

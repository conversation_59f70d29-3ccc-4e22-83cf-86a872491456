import { TimeAttendance } from '../../../models/time-attendance.model';
import { User } from 'src/models/users-models/user.model';
import { CompanySettings } from 'src/models/company-settings.model';
import { DEFAULT_WORKING_DAYS } from 'src/constants/attendance.constants';
import { buildAttendanceDaySummary } from './attendance-day.builder';

export interface CompanySettingsData {
  workingDays: string[];
  holidays: string[];
}

export interface AttendanceActivity {
  id: number;
  clockInTime: string | null;
  clockOutTime: string | null;
  notes: string | null;
}

export interface UserAttendanceSummary {
  date: string;
  day: string;
  checkIn: string | null;
  checkOut: string | null;
  workedHours: string;
  overtime: string;
  status: string;
  lateBy: string;
  dayType: string;
  isHoliday: boolean;
  attendanceActivities: AttendanceActivity[];
}

export async function getCompanySettings(
  companyId: number,
): Promise<CompanySettingsData> {
  const companySettings = await CompanySettings.findOne({
    where: { companyId },
  });

  return {
    workingDays: companySettings?.workingDays || DEFAULT_WORKING_DAYS,
    holidays: (companySettings?.holidayDays || []).map((d) =>
      new Date(d).toLocaleDateString('en-US'),
    ),
  };
}

export function groupAttendancesByUser(
  timeAttendances: TimeAttendance[],
): Record<number, AttendanceActivity[]> {
  return timeAttendances.reduce(
    (acc, record) => {
      if (!acc[record.userId]) {
        acc[record.userId] = [];
      }
      acc[record.userId].push({
        id: record.id,
        clockInTime: record.clockInTime
          ? record.clockInTime.toISOString()
          : null,
        clockOutTime: record.clockOutTime
          ? record.clockOutTime.toISOString()
          : null,
        notes: record.notes || null,
      });
      return acc;
    },
    {} as Record<number, AttendanceActivity[]>,
  );
}

export function buildUserAttendanceSummary(
  employee: Partial<User>,
  date: Date,
  activities: AttendanceActivity[],
  workingDays: string[],
  holidays: string[],
): UserAttendanceSummary {
  const timeZone = employee.timeZone || 'Asia/Dubai';
  const attendanceSummary = buildAttendanceDaySummary(
    date,
    activities,
    workingDays,
    holidays,
    timeZone,
  );

  return {
    date: attendanceSummary.date,
    day: attendanceSummary.day,
    checkIn: attendanceSummary.checkIn,
    checkOut: attendanceSummary.checkOut,
    workedHours: attendanceSummary.workedHours,
    overtime: attendanceSummary.overtime,
    status: attendanceSummary.status,
    lateBy: attendanceSummary.lateBy,
    dayType: attendanceSummary.dayType,
    isHoliday: attendanceSummary.isHoliday,
    attendanceActivities: attendanceSummary.attendanceActivities || [],
  };
}

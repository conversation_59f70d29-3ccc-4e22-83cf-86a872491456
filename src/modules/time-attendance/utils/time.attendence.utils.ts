import { isLateClockIn } from 'src/utils/date.util';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { utcToLocal, getLocalDateString } from 'src/utils/date.utils';

export function calculateLateAndAbsentDays(
  attendances: TimeAttendance[],
  daysInMonth: number,
  month: number,
  year: number,
  today: Date,
  timeZone: string,
) {
  const clockInMap = new Map<string, Date>();

  // Ensure only one clock-in per day is evaluated
  for (const att of attendances) {
    if (!att.clockInTime) continue; // Skip records without clock-in time
    // Convert clockInTime to user's local date string
    const localClockIn = utcToLocal(att.clockInTime, timeZone);
    const dateStr = getLocalDateString(localClockIn, timeZone);
    const existing = clockInMap.get(dateStr);
    if (!existing || localClockIn < existing) {
      clockInMap.set(dateStr, localClockIn);
    }
  }

  let lateDays = 0;
  for (const [_, clockInTime] of clockInMap.entries()) {
    if (isLateClockIn(clockInTime, timeZone)) {
      lateDays++;
    }
  }

  let absentDays = 0;
  for (let i = 1; i <= daysInMonth; i++) {
    // Create date in user's timezone
    const date = new Date(Date.UTC(year, month - 1, i));
    const localDate = new Date(date.toLocaleString('en-US', { timeZone }));

    // Stop if it's today (for current month)
    if (
      localDate.getFullYear() === today.getFullYear() &&
      localDate.getMonth() === today.getMonth() &&
      localDate.getDate() === today.getDate()
    )
      break;

    // Skip weekends
    const dayOfWeek = localDate.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) continue;

    if (!clockInMap.has(localDate.toDateString())) {
      absentDays++;
    }
  }

  return {
    workedDays: clockInMap.size,
    LateDays: lateDays,
    absentDays,
  };
}

export function calculateWorkedHours(
  attendances: TimeAttendance[],
  days: number,
  timeZone: string,
) {
  const plannedHours = days * 9;
  let workedHours = 0;

  for (const att of attendances) {
    if (att.clockInTime && att.clockOutTime) {
      // Convert to user's local time
      const clockIn = utcToLocal(att.clockInTime, timeZone);
      const clockOut = utcToLocal(att.clockOutTime, timeZone);
      const hours = (clockOut.getTime() - clockIn.getTime()) / (1000 * 60 * 60);
      workedHours += hours;
    }
  }

  return {
    plannedHours,
    workedHours: Number(workedHours.toFixed(2)),
    difference: Number((workedHours - plannedHours).toFixed(2)),
  };
}

export function calculateOvertime(
  attendances: TimeAttendance[],
  timeZone: string,
) {
  let overtimeHours = 0;

  for (const att of attendances) {
    if (att.clockInTime && att.clockOutTime) {
      // Convert to user's local time
      const clockIn = utcToLocal(att.clockInTime, timeZone);
      const clockOut = utcToLocal(att.clockOutTime, timeZone);
      const hours = (clockOut.getTime() - clockIn.getTime()) / (1000 * 60 * 60);
      if (hours > 9) overtimeHours += hours - 9;
    }
  }

  return { totalOvertimeHours: Number(overtimeHours.toFixed(2)) };
}

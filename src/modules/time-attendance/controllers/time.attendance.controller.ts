import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
  Req,
  Query,
  Put,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { EmployeeTimeAttendanceService } from '../services/time-attendance.service';
import {
  AuthenticatedOnly,
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
@Controller('time-attendance')
@UseGuards(JwtAuthGuard)
export class TimeAttendanceController {
  constructor(
    private readonly timeAttendanceService: EmployeeTimeAttendanceService,
  ) {}

  @Post('clock-in')
  @AuthenticatedOnly()
  async clockIn(@Req() req: UserRequestI): Promise<Partial<TimeAttendance>> {
    const user = req.user;
    return this.timeAttendanceService.clockIn(user);
  }

  @Patch('clock-out')
  @AuthenticatedOnly()
  async clockOut(@Req() req: UserRequestI): Promise<TimeAttendance> {
    const user = req.user;
    return this.timeAttendanceService.clockOut(user);
  }

  @Get('by-date')
  @AuthenticatedOnly()
  async findByDate(
    @Req() req: UserRequestI,
    @Query('date') date: string,
  ): Promise<TimeAttendance[]> {
    if (!date) date = new Date().toISOString().split('T')[0];
    return this.timeAttendanceService.findByDate(req.user.id, date);
  }

  @Get('report')
  @AuthenticatedOnly()
  async getMonthlyReport(
    @Query('month') month: number,
    @Query('year') year: number,
  ) {
    return this.timeAttendanceService.generateMonthlyReport({ month, year });
  }

  @Get('summary')
  @AuthenticatedOnly()
  async getAttendanceSummary(
    @Query('start') start: string,
    @Query('end') end: string,
    @Query('userId') userId: number,
    @Req() req: UserRequestI,
  ) {
    const admin = req.user;
    return this.timeAttendanceService.getAttendanceSummary(
      admin,
      start,
      end,
      userId,
    );
  }

  @Get('company-employees')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getCompanyEmployeesTimeAttendance(
    @Req() req: UserRequestI,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('q') q: string,
  ) {
    return this.timeAttendanceService.getCompanyEmployeesTimeAttendance(
      req.user,
      parseInt(page),
      parseInt(limit),
      q,
    );
  }

  @Get('employee/:userId')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getLatestAttendanceForEmployee(
    @Param('userId') userId: number,
    @Req() req: UserRequestI,
  ) {
    return this.timeAttendanceService.getLatestAttendanceByUserId(
      req.user,
      userId,
    );
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async update(
    @Param('id') id: number,
    @Body() updateTimeAttendanceDto: UpdateTimeAttendanceDto,
    @Req() req: UserRequestI,
  ): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.update(
      id,
      updateTimeAttendanceDto,
      req.user,
    );
  }
}

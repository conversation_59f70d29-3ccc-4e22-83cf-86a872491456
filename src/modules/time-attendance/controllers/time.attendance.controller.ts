import {
  Controller,
  Get,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { AuthenticatedOnly } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { EmployeeTimeAttendanceService } from '../services/time-attendance.service';

@Controller('time-attendance')
@UseGuards(JwtAuthGuard)
export class TimeAttendanceController {
  constructor(
    private readonly timeAttendanceService: EmployeeTimeAttendanceService,
  ) {}

  @Post('clock-in')
  @AuthenticatedOnly()
  async clockIn(@Req() req: UserRequestI): Promise<Partial<TimeAttendance>> {
    const user = req.user;
    return this.timeAttendanceService.clockIn(user);
  }

  @Patch('clock-out')
  @AuthenticatedOnly()
  async clockOut(@Req() req: UserRequestI): Promise<TimeAttendance> {
    const user = req.user;
    return this.timeAttendanceService.clockOut(user);
  }

  @Get('by-date')
  @AuthenticatedOnly()
  async findByDate(
    @Req() req: UserRequestI,
    @Query('date') date: string,
  ): Promise<TimeAttendance[]> {
    if (!date) date = new Date().toISOString().split('T')[0];
    return this.timeAttendanceService.findByDate(req.user.id, date);
  }

  @Get('report')
  @AuthenticatedOnly()
  async getMonthlyReport(
    @Query('month') month: number,
    @Query('year') year: number,
  ) {
    return this.timeAttendanceService.generateMonthlyReport({ month, year });
  }

  @Get('summary')
  @AuthenticatedOnly()
  async getAttendanceSummary(
    @Query('start') start: string,
    @Query('end') end: string,
    @Req() req: UserRequestI,
  ) {
    const companyId = req.user.companyId;
    return this.timeAttendanceService.getAttendanceSummary(
      req.user.id,
      companyId,
      start,
      end,
    );
  }
}

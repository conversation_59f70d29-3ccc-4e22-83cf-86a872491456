import { Controller, Get, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { AuthenticatedOnly } from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { EmployeeTimeAttendanceService } from '../services/time-attendance.service';

@Controller('time-attendance')
@UseGuards(JwtAuthGuard)
export class TimeAttendanceController {
  constructor(
    private readonly timeAttendanceService: EmployeeTimeAttendanceService,
  ) {}

  @Post('clock-in')
  @AuthenticatedOnly()
  async clockIn(
    @GetUser() user: RequestUserObjectI,
  ): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.clockIn(user);
  }

  @Patch('clock-out')
  @AuthenticatedOnly()
  async clockOut(@GetUser() user: RequestUserObjectI): Promise<TimeAttendance> {
    return this.timeAttendanceService.clockOut(user);
  }

  @Get('by-date')
  @AuthenticatedOnly()
  async findByDate(
    @GetUser('id') userId: number,
    @Query('date') date: string,
  ): Promise<TimeAttendance[]> {
    if (!date) date = new Date().toISOString().split('T')[0];
    return this.timeAttendanceService.findByDate(userId, date);
  }

  @Get('report')
  @AuthenticatedOnly()
  async getMonthlyReport(
    @Query('month') month: number,
    @Query('year') year: number,
    @GetUser('id') userId: number,
  ) {
    return this.timeAttendanceService.generateMonthlyReport({
      month,
      year,
      userId,
    });
  }

  @Get('summary')
  @AuthenticatedOnly()
  async getAttendanceSummary(
    @Query('start') start: string,
    @Query('end') end: string,
    @GetUser() user: RequestUserObjectI,
  ) {
    return this.timeAttendanceService.getAttendanceSummary(
      { userId: user.id },
      start,
      end,
    );
  }
}

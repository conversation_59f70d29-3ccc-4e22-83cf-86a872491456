import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import {
  RequestUserObjectI,
  UserRequestI,
} from 'src/modules/auth/auth.interfaces';
import {
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { EmployeeTimeAttendanceService } from '../services/time-attendance.service';

@Controller('company/time-attendance')
@UseGuards(JwtAuthGuard)
export class CompanyTimeAttendanceController {
  constructor(
    private readonly timeAttendanceService: EmployeeTimeAttendanceService,
  ) {}

  @Get('employees')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getCompanyEmployeesTimeAttendance(
    @GetUser('companyId') companyId: number,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('q') q: string,
  ) {
    return this.timeAttendanceService.getCompanyEmployeesTimeAttendance(
      companyId,
      parseInt(page),
      parseInt(limit),
      q,
    );
  }

  @Get('employees/:userId')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getLatestAttendanceForEmployee(
    @Param('userId') userId: number,
    @GetUser('companyId') companyId: number,
  ) {
    return this.timeAttendanceService.getLatestAttendanceByUserId(
      companyId,
      userId,
    );
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async update(
    @Param('id') id: number,
    @Body() updateTimeAttendanceDto: UpdateTimeAttendanceDto,
    @GetUser('companyId') companyId: number,
  ): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.update(
      id,
      updateTimeAttendanceDto,
      companyId,
    );
  }

  @Get('summary')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getAttendanceSummary(
    @Query('start') start: string,
    @Query('end') end: string,
    @Query('userId') userId: number,
    @GetUser() user: RequestUserObjectI,
  ) {
    return this.timeAttendanceService.getAttendanceSummary(
      { userId: userId ?? user.id, companyId: user.companyId },
      start,
      end,
    );
  }
}

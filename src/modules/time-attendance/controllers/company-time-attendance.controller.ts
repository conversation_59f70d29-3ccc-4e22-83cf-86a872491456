import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import {
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { TimeAttendance } from '../../../models/time-attendance.model';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UpdateTimeAttendanceDto } from '../dto/update-time-attendance.dto';
import { EmployeeTimeAttendanceService } from '../services/time-attendance.service';

@Controller('company/time-attendance')
@UseGuards(JwtAuthGuard)
export class CompanyTimeAttendanceController {
  constructor(
    private readonly timeAttendanceService: EmployeeTimeAttendanceService,
  ) {}

  @Get('employees')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getCompanyEmployeesTimeAttendance(
    @Req() req: UserRequestI,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('q') q: string,
  ) {
    return this.timeAttendanceService.getCompanyEmployeesTimeAttendance(
      req.user,
      parseInt(page),
      parseInt(limit),
      q,
    );
  }

  @Get('employees/:userId')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getLatestAttendanceForEmployee(
    @Param('userId') userId: number,
    @Req() req: UserRequestI,
  ) {
    return this.timeAttendanceService.getLatestAttendanceByUserId(
      req.user,
      userId,
    );
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async update(
    @Param('id') id: number,
    @Body() updateTimeAttendanceDto: UpdateTimeAttendanceDto,
    @Req() req: UserRequestI,
  ): Promise<Partial<TimeAttendance>> {
    return this.timeAttendanceService.update(
      id,
      updateTimeAttendanceDto,
      req.user,
    );
  }

  @Get('summary')
  @CanView(PLATFORM_SECTION_ENUM.TIME_ATTENDANCE)
  async getAttendanceSummary(
    @Query('start') start: string,
    @Query('end') end: string,
    @Query('userId') userId: number,
    @Req() req: UserRequestI,
  ) {
    const companyId = req.user.companyId;
    const user = userId ? userId : req.user.id;
    return this.timeAttendanceService.getAttendanceSummary(
      user,
      companyId,
      start,
      end,
    );
  }
}

import {
  Controller,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Delete,
  Param,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UserRequestI } from 'src/modules/auth/auth.interfaces';
import { JwtAuthGuard } from 'src/modules/auth/guards/jwt-auth.guard';
import { ThrottleRelaxed } from 'src/modules/throttling';
import { FilesService } from '../services/files.service';

@Controller('files')
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @Post('upload')
  @UseGuards(JwtAuthGuard)
  // @CanCreate(PLATFORM_SECTION_ENUM.DOCUMENTS)
  @ThrottleRelaxed()
  @UseInterceptors(FileInterceptor('file'))
  async createWithUpload(
    @UploadedFile() file: any,
    @Req() req: UserRequestI,
    @Query('userId') userId: number,
  ) {
    console.log(req.user);
    const authHeader = req.headers['authorization'] || '';
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;
    return this.filesService.uploadFile(file, token, userId || 0);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)

  // @CanDelete(PLATFORM_SECTION_ENUM.DOCUMENTS)
  @ThrottleRelaxed()
  async deleteFile(@Param('fileId') fileId: string, @Req() req: UserRequestI) {
    console.log(fileId);
    const authHeader = req.headers['authorization'] || '';
    const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;
    return this.filesService.deleteFile(fileId, token);
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import {
  Can<PERSON><PERSON>,
  CanDelete,
  CanEdit,
  CanView,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { ThrottleRelaxed } from 'src/modules/throttling';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { AssignAssetDto } from '../dto/assign-asset.dto';
import { CreateAssetDto } from '../dto/create-asset.dto';
import { GetPagiantedAssetsDTO } from '../dto/get-paginated-assets.dto';
import { UpdateAssetDto } from '../dto/update-asset.dto';
import { AssetsService } from '../services/assets.service';

@Controller('company/assets')
@UseGuards(PermissionGuard)
export class CompanyAssetsController {
  constructor(private readonly assetsService: AssetsService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async create(
    @Body() createAssetDto: CreateAssetDto,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.assetsService.create(createAssetDto, companyId);
    return {
      success: true,
      message: 'Asset created successfully',
      data: result,
    };
  }

  @Get('get')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async findAll(
    @GetUser('companyId') companyId: number,
    @Query() getPaginatedAssetsDto: GetPagiantedAssetsDTO,
  ) {
    const result = await this.assetsService.findAll(
      companyId,
      getPaginatedAssetsDto.page,
      getPaginatedAssetsDto.limit,
      getPaginatedAssetsDto.search,
      getPaginatedAssetsDto.assetType,
      getPaginatedAssetsDto.status,
      getPaginatedAssetsDto.assignedTo,
    );
    return result;
  }

  @Get('stats')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async getStats(@GetUser('companyId') companyId: number) {
    const result = await this.assetsService.getAssetStats(companyId);
    return result;
  }

  @Get('by-id/:id')
  @CanView(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.assetsService.findOne(id, companyId);
    return result;
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAssetDto: UpdateAssetDto,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.assetsService.update(
      id,
      updateAssetDto,
      companyId,
    );
    return result;
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @GetUser('companyId') companyId: number,
  ) {
    await this.assetsService.remove(id, companyId);
    return {
      message: 'Asset deleted successfully',
    };
  }

  @Post(':id/assign')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async assignAsset(
    @Param('id', ParseIntPipe) id: number,
    @Body() assignAssetDto: AssignAssetDto,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.assetsService.assignAsset(
      id,
      assignAssetDto,
      companyId,
    );
    return result;
  }

  @Post(':id/unassign')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  @ThrottleRelaxed()
  async unassignAsset(
    @Param('id', ParseIntPipe) id: number,
    @Body() unassignAssetDto: AssignAssetDto,
    @GetUser('companyId') companyId: number,
  ) {
    const result = await this.assetsService.unassignAsset(
      id,
      unassignAssetDto,
      companyId,
    );
    return result;
  }
}

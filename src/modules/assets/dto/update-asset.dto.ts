import {
  IsString,
  <PERSON>Enum,
  IsOptional,
  IsDateString,
  IsNumber,
  IsInt,
  MaxLength,
  MinLength,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AssetStatus } from '../../../models/asset.model';

export class UpdateAssetDto {
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  assetTag?: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  assetType?: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  modelSoftware?: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  osVersion?: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  licenseSerial?: string;

  @IsOptional()
  @IsDateString()
  purchaseDate?: Date;

  @IsOptional()
  @IsDateString()
  expirationDate?: Date;

  @IsOptional()
  @IsEnum(AssetStatus)
  status?: AssetStatus;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  assignedTo?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Type(() => Number)
  purchasePrice?: number;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  serialNumber?: string;
}

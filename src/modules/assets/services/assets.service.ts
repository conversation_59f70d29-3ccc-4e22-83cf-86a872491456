import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Asset, AssetStatus } from '../../../models/asset.model';
import { User } from '../../../models/users-models/user.model';
import { CreateAssetDto } from '../dto/create-asset.dto';
import { UpdateAssetDto } from '../dto/update-asset.dto';
import { AssignAssetDto } from '../dto/assign-asset.dto';
import { CrudHelperService } from '../../../common/crud-helper/crud-helper.service';
import { Op } from 'sequelize';

@Injectable()
export class AssetsService {
  constructor(
    @InjectModel(Asset)
    private assetModel: typeof Asset,
    @InjectModel(User)
    private userModel: typeof User,
    private sequelize: Sequelize,
    private crudHelperService: CrudHelperService,
  ) {}

  async create(dto: CreateAssetDto, companyId: number): Promise<Asset> {
    const transaction = await this.sequelize.transaction();

    try {
      // Check if asset tag already exists for this company
      const existingAsset = await this.crudHelperService.findOne(
        this.assetModel,
        {
          where: {
            assetTag: dto.assetTag,
            companyId,
          },
        },
      );

      if (existingAsset) {
        throw new BadRequestException('Asset tag already exists');
      }

      // If assignedTo is provided, validate the user exists and belongs to company
      if (dto.assignedTo) {
        const user = await this.crudHelperService.findOne(this.userModel, {
          where: {
            id: dto.assignedTo,
            companyId,
          },
        });

        if (!user) {
          throw new NotFoundException(
            'Assigned user not found or not authorized',
          );
        }

        // If assigning to user, set status to IN_USE
        dto.status = AssetStatus.IN_USE;
      }

      const asset = (await this.crudHelperService.create(
        this.assetModel,
        {
          ...dto,
          companyId,
        },
        transaction,
      )) as Asset;

      await transaction.commit();

      return await this.findOne(asset.id, companyId);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async findAll(
    companyId: number,
    page: number = 1,
    limit: number = 10,
    search?: string,
    assetType?: string,
    status?: string,
    assignedTo?: number,
  ) {
    const where: any = {
      companyId,
    };

    if (search) {
      where[Op.or] = [
        { assetTag: { [Op.iLike]: `%${search}%` } },
        {
          modelSoftware: { [Op.iLike]: `%${search}%` },
        },
        {
          serialNumber: { [Op.iLike]: `%${search}%` },
        },
        {
          licenseSerial: { [Op.iLike]: `%${search}%` },
        },
      ];
    }

    if (assetType) {
      where.assetType = assetType;
    }

    if (status) {
      where.status = status;
    }

    if (assignedTo) {
      where.assignedTo = assignedTo;
    }

    return this.crudHelperService.paginateWithQuery(this.assetModel, {
      page,
      limit,
      where,
      include: [
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false,
        },
      ],
      order: [['createdAt', 'DESC']],
    });
  }

  async findOne(id: number, companyId: number): Promise<Asset> {
    const asset = (await this.crudHelperService.findOne(this.assetModel, {
      where: {
        id,
        companyId,
      },
      include: [
        {
          model: User,
          as: 'assignedUser',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          required: false,
        },
      ],
    })) as Asset;

    if (!asset) {
      throw new NotFoundException('Asset not found or not authorized');
    }

    return asset;
  }

  async update(
    id: number,
    dto: UpdateAssetDto,
    companyId: number,
  ): Promise<Asset> {
    const transaction = await this.sequelize.transaction();

    try {
      const asset = await this.findOne(id, companyId);

      // Check if asset tag is being changed and if it already exists
      if (dto.assetTag && dto.assetTag !== asset.assetTag) {
        const existingAsset = await this.crudHelperService.findOne(
          this.assetModel,
          {
            where: {
              assetTag: dto.assetTag,
              companyId,
              id: { [Op.ne]: id },
            },
          },
        );

        if (existingAsset) {
          throw new BadRequestException('Asset tag already exists');
        }
      }

      // If assignedTo is being changed, validate the user
      if (dto.assignedTo !== undefined) {
        if (dto.assignedTo) {
          const user = await this.crudHelperService.findOne(this.userModel, {
            where: {
              id: dto.assignedTo,
              companyId,
            },
          });

          if (!user) {
            throw new NotFoundException(
              'Assigned user not found or not authorized',
            );
          }

          // If assigning to user, set status to IN_USE
          dto.status = AssetStatus.IN_USE;
        } else {
          // If unassigning, set status to AVAILABLE
          dto.status = AssetStatus.AVAILABLE;
        }
      }

      await this.crudHelperService.update(
        this.assetModel,
        dto,
        { where: { id } },
        transaction,
      );
      await transaction.commit();

      return await this.findOne(id, companyId);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async remove(id: number, companyId: number): Promise<void> {
    const asset = await this.findOne(id, companyId);
    await this.crudHelperService.delete(this.assetModel, { where: { id } });
  }

  async assignAsset(
    id: number,
    dto: AssignAssetDto,
    companyId: number,
  ): Promise<Asset> {
    const transaction = await this.sequelize.transaction();

    try {
      const asset = await this.findOne(id, companyId);

      if (asset.assignedTo) {
        throw new BadRequestException('Asset is already assigned to a user');
      }

      // Validate the user exists and belongs to company
      const user = await this.crudHelperService.findOne(this.userModel, {
        where: {
          id: dto.userId,
          companyId,
        },
      });

      if (!user) {
        throw new NotFoundException('User not found or not authorized');
      }

      await this.crudHelperService.update(
        this.assetModel,
        {
          assignedTo: dto.userId,
          status: AssetStatus.IN_USE,
        },
        { where: { id } },
        transaction,
      );

      await transaction.commit();

      return await this.findOne(id, companyId);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async unassignAsset(
    id: number,
    dto: AssignAssetDto,
    companyId: number,
  ): Promise<Asset> {
    const transaction = await this.sequelize.transaction();

    try {
      const asset = await this.findOne(id, companyId);

      if (!asset.assignedTo) {
        throw new BadRequestException('Asset is not assigned to any user');
      }

      await this.crudHelperService.update(
        this.assetModel,
        {
          assignedTo: null,
          status: AssetStatus.AVAILABLE,
        },
        { where: { id } },
        transaction,
      );

      await transaction.commit();

      return await this.findOne(id, companyId);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async getAssetStats(companyId: number) {
    const totalAssets = await this.crudHelperService.count(this.assetModel, {
      companyId,
    });

    const inUseAssets = await this.crudHelperService.count(this.assetModel, {
      companyId,
      status: AssetStatus.IN_USE,
    });

    const availableAssets = await this.crudHelperService.count(
      this.assetModel,
      {
        companyId,
        status: AssetStatus.AVAILABLE,
      },
    );

    const maintenanceAssets = await this.crudHelperService.count(
      this.assetModel,
      {
        companyId,
        status: AssetStatus.MAINTENANCE,
      },
    );

    return {
      totalAssets,
      inUseAssets,
      availableAssets,
      maintenanceAssets,
    };
  }
}

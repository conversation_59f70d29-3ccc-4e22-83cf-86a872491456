import { Module, forwardRef } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { User } from 'src/models/users-models/user.model';
import { RolesModule } from 'src/modules/roles-and-permissions/roles/roles.module';
import { Company } from '../../models/company.model';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { CompanyController } from './controllers/company.controller';
import { CompanyService } from './services/company.service';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Company,
      EmploymentDetails,
      UserRole,
      User,
      Role,
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => RolesModule),
    forwardRef(() => AuthModule),
  ],
  controllers: [CompanyController],
  providers: [CompanyService],
  exports: [CompanyService],
})
export class CompanyModule {}

import {
  ConflictException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { CrudHelperService } from 'src/common/crud-helper/crud-helper.service';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { User } from 'src/models/users-models/user.model';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';
import { RolesService } from 'src/modules/roles-and-permissions/roles/roles.service';
import { UsersService } from 'src/modules/users/services/users.service';
import { RedirectSectionEnum } from 'src/utils/redirect-section.enum';

import { ConfigService } from '@nestjs/config';
import { AppConfig } from 'src/config/config.interface';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { AuthService } from 'src/modules/auth/services/auth.service';
import { Company } from '../../../models/company.model';
import { CreateCompanyDto } from '../dto/create-company.dto';
import { UpdateCompanyDto } from '../dto/update-company.dto';

@Injectable()
export class CompanyService {
  constructor(
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(EmploymentDetails)
    private readonly employmentDetailsModel: typeof EmploymentDetails,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    private readonly crudHelperService: CrudHelperService,
    @Inject(forwardRef(() => RolesService))
    private readonly rolesService: RolesService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: UsersService,
    private readonly sequelize: Sequelize,
    @Inject(forwardRef(() => AuthService))
    private readonly authService: AuthService,
    private readonly configService: ConfigService<AppConfig>,
  ) {}

  async create(
    user: RequestUserObjectI,
    createCompanyDto: CreateCompanyDto,
  ): Promise<Partial<Company>> {
    // Start transaction
    const transaction = await this.sequelize.transaction();

    try {
      const [existingName, existingPortalName] = await Promise.all([
        this.crudHelperService.findOne(
          this.companyModel,
          {
            where: { name: createCompanyDto.name },
          },
          transaction,
        ),
        this.crudHelperService.findOne(
          this.companyModel,
          {
            where: { portalName: createCompanyDto.portalName },
          },
          transaction,
        ),
      ]);

      if (existingName) {
        throw new ConflictException('Company with this name already exists');
      }

      if (existingPortalName) {
        throw new ConflictException(
          'Company with this portal name already exists',
        );
      }

      const company = await this.crudHelperService.create<Company>(
        this.companyModel,
        {
          name: createCompanyDto.name,
          portalName: createCompanyDto.portalName,
          industry: createCompanyDto.industry,
          website: createCompanyDto.website,
          email: createCompanyDto.email,
          userId: user.id,
        },
        transaction,
      );

      await this.crudHelperService.update(
        this.userModel,
        { companyId: company.id },
        { where: { id: user.id } },
        transaction,
      );

      // Get the user's role from UserRole model
      const userRole = await this.crudHelperService.findOne<UserRole>(
        this.userRoleModel,
        {
          where: { userId: user.id },
        },
        transaction,
      );

      if (userRole) {
        // Update the role to have the company id
        await this.crudHelperService.update(
          this.roleModel,
          { companyId: company.id },
          { where: { id: userRole.roleId } },
          transaction,
        );
      }

      const isDashboardRedirect =
        user.redirectTo === RedirectSectionEnum.DASHBOARD;

      if (!isDashboardRedirect) {
        await this.usersService.updateUser(
          user.id,
          { redirectTo: RedirectSectionEnum.CREATE_DEPARTMENT },
          transaction,
        );
      }

      // Commit transaction
      await transaction.commit();

      // Give permissions to company super admin (outside transaction to avoid conflicts)
      await this.rolesService.giveCompanySuperAdminPermissionsByCompanyId(
        company.id,
      );

      return company;
    } catch (error) {
      console.error('Original error in CompanyService.create:', error);
      try {
        await transaction.rollback();
        console.log('Transaction rolled back due to error:', error.message);
      } catch (rollbackError) {
        console.log(
          'Transaction rollback failed (likely already rolled back):',
          rollbackError.message,
        );
      }
      throw error;
    }
  }

  async checkPortalNameUnique(portalName: string): Promise<boolean> {
    const existingPortalName = await this.crudHelperService.findOne(
      this.companyModel,
      {
        where: { portalName },
      },
    );
    return !existingPortalName;
  }

  async findAll(): Promise<Partial<Company>[]> {
    return this.crudHelperService.findAll<Company>(this.companyModel, {
      include: ['address', 'contactDetails'],
    });
  }

  async findOne(id: number): Promise<Partial<Company>> {
    const company = await this.crudHelperService.findOne<Company>(
      this.companyModel,
      {
        where: { id },
        include: ['address', 'contactDetails'],
      },
    );

    if (!company) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    return company;
  }

  async update(
    id: number,
    updateCompanyDto: UpdateCompanyDto,
  ): Promise<Partial<Company>> {
    // Start transaction
    const transaction = await this.sequelize.transaction();

    try {
      await this.findOne(id);

      if (updateCompanyDto.name) {
        const existingName = await this.crudHelperService.findOne<Company>(
          this.companyModel,
          {
            where: { name: updateCompanyDto.name },
          },
          transaction,
        );
        if (existingName && (existingName as any).id !== id) {
          throw new ConflictException('Company with this name already exists');
        }
      }

      if (updateCompanyDto.portalName) {
        const existingPortalName =
          await this.crudHelperService.findOne<Company>(
            this.companyModel,
            {
              where: { portalName: updateCompanyDto.portalName },
            },
            transaction,
          );
        if (existingPortalName && (existingPortalName as any).id !== id) {
          throw new ConflictException(
            'Company with this portal name already exists',
          );
        }
      }

      const result = await this.crudHelperService.update<Company>(
        this.companyModel,
        updateCompanyDto,
        {
          where: { id },
        },
        transaction,
      );

      // Commit transaction
      await transaction.commit();

      return result;
    } catch (error) {
      // Rollback transaction on any error
      await transaction.rollback();
      throw error;
    }
  }

  async remove(id: number): Promise<void> {
    // Start transaction
    const transaction = await this.sequelize.transaction();

    try {
      await this.crudHelperService.delete(
        this.companyModel,
        {
          where: { id },
        },
        transaction,
      );

      // Commit transaction
      await transaction.commit();
    } catch (error) {
      // Rollback transaction on any error
      await transaction.rollback();
      throw error;
    }
  }

  async findByUserId(userId: number): Promise<Partial<Company>> {
    const user = await this.crudHelperService.findOne<User>(this.userModel, {
      where: { id: userId },
      include: [Company],
    });

    if (!user) {
      throw new NotFoundException(`User not found: ${userId}`);
    }

    if (!user.companyId) {
      throw new NotFoundException(
        `User ${userId} is not associated with any company`,
      );
    }

    return this.crudHelperService.findOne<Company>(this.companyModel, {
      where: { id: user.companyId },
    });
  }

  async getMyCompanies(userId: number): Promise<Partial<Company>[]> {
    console.log('userId', userId);
    return this.companyModel.findAll({
      where: {
        userId,
      },
    });
  }

  async switchCompany(
    companyId: number,
    user: RequestUserObjectI,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const company = await this.companyModel.findOne({
      where: { id: companyId, userId: user.id },
    });
    if (!company) {
      throw new NotFoundException(`Company with not found`);
    }
    const projectId =
      this.configService.get<AppConfig['fileStorage']>('fileStorage').projectId;
    return await this.authService.generateTokens({
      ...user,
      sub: user.id,
      companyId: company.id,
      projectId,
      scope: user.role.scope,
      role: user.role.name,
    });
  }
}

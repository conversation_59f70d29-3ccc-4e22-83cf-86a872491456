import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards
} from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import {
  RequestUserObjectI
} from 'src/modules/auth/auth.interfaces';
import { PermissionGuard } from 'src/modules/auth/guards/permission.guard';
import {
  CanCreate,
  CanDelete,
  CanEdit,
} from 'src/modules/roles-and-permissions/permissions/decorators/permission.decorator';
import { PLATFORM_SECTION_ENUM } from 'src/utils/constants';
import { Company } from '../../../models/company.model';
import { CreateCompanyDto } from '../dto/create-company.dto';
import { UpdateCompanyDto } from '../dto/update-company.dto';
import { CompanyService } from '../services/company.service';

@Controller('companies')
@UseGuards(PermissionGuard)
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Post()
  @CanCreate(PLATFORM_SECTION_ENUM.COMPANY)
  async create(
    @GetUser() user: RequestUserObjectI,
    @Body() createCompanyDto: CreateCompanyDto,
  ): Promise<Partial<Company>> {
    return this.companyService.create(user, createCompanyDto);
  }

  @Get()
  async findAll(): Promise<Partial<Company>[]> {
    return this.companyService.findAll();
  }

  @Get('mine')
  async getMyCompanies(
    @GetUser('id') userId: number,
  ): Promise<Partial<Company>[]> {
    return this.companyService.getMyCompanies(userId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: string,
  ): Promise<Partial<Company>> {
    return this.companyService.findOne(+id);
  }

  @Get('switch/:id')
  async switchCompany(
    @Param('id') companyId: string,
    @GetUser() user: RequestUserObjectI,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    return this.companyService.switchCompany(+companyId, user);
  }

  @Get('check-portal-name-unique/:portalName')
  async checkPortalNameUnique(
    @Param('portalName') portalName: string,
  ): Promise<boolean> {
    return this.companyService.checkPortalNameUnique(portalName);
  }

  @Put(':id')
  @CanEdit(PLATFORM_SECTION_ENUM.COMPANY)
  async update(
    @Param('id') id: string,
    @Body() updateCompanyDto: UpdateCompanyDto,
  ): Promise<Partial<Company>> {
    return this.companyService.update(+id, updateCompanyDto);
  }

  @Delete(':id')
  @CanDelete(PLATFORM_SECTION_ENUM.COMPANY)
  async remove(@Param('id') id: string): Promise<void> {
    return this.companyService.remove(+id);
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards
} from '@nestjs/common';
import { GetUser } from 'src/common/decorators';
import { BankInfo } from 'src/models/users-models/bank-info.model';
import { PLATFORM_SECTION_ENUM } from '../../../utils/constants';
import { PermissionGuard } from '../../auth/guards/permission.guard';
import {
  AuthenticatedOnly,
  CanView,
} from '../../roles-and-permissions/permissions/decorators/permission.decorator';
import { CreateBankInfoDto } from '../dto/create-bank-info.dto';
import { UpdateBankInfoDto } from '../dto/update-bank-info.dto';
import { BankInfoService } from '../services/bank-info.service';

@Controller('bank-info')
@UseGuards(PermissionGuard)
export class BankInfoController {
  constructor(private readonly bankInfoService: BankInfoService) {}

  @Post()
  @AuthenticatedOnly()
  async create(
    @Body() dto: CreateBankInfoDto,
    @GetUser('companyId') companyId: number,
  ): Promise<Partial<BankInfo>> {
    return this.bankInfoService.create(dto, companyId);
  }

  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @GetUser('companyId') companyId: number,
  ) {
    return this.bankInfoService.findOne(id, companyId);
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateBankInfoDto,
    @GetUser('companyId') companyId: number,
  ) {
    return this.bankInfoService.update(id, dto, companyId);
  }

  @Delete(':id')
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @GetUser('companyId') companyId: number,
  ) {
    return this.bankInfoService.remove(id, companyId);
  }

  @Get('user/:userId')
  @CanView(PLATFORM_SECTION_ENUM.EMPLOYEES)
  async findByUserId(
    @Param('userId', ParseIntPipe) userId: number,
    @GetUser('companyId') companyId: number,
  ) {
    return this.bankInfoService.findByUserId(userId, companyId);
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import * as path from 'path';
import * as hbs from 'hbs';
import * as fs from 'fs';
import * as puppeteer from 'puppeteer';
import { InterviewInvitationData } from '../users/interfaces/interview-invitation.interface';
import { pathToFileURL } from 'url';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private readonly mailerService: MailerService) {}

  async generatePdfFromTemplate(templateName: string, data: any) {
    const logoPath = path.resolve(__dirname, 'templates', 'assets', 'logo.png');

    if (!fs.existsSync(logoPath)) {
      console.error('❌ Logo file not found at:', logoPath);
    } else {
      console.log('✅ Logo file found at:', logoPath);
    }

    const logoUrl = pathToFileURL(logoPath).href;
    const templatePath = path.join(
      __dirname,
      'templates',
      `${templateName}.hbs`,
    );
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const compiled = hbs.compile(templateHtml);
    const html = compiled(data);

    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });

    const page = await browser.newPage();

    // Allow file:// images to be rendered
    await page.setContent(html, { waitUntil: 'networkidle0' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
    });

    await browser.close();

    return pdfBuffer;
  }
  async sendOtpEmail(email: string, otp: string): Promise<void> {
    const subject = 'Your OTP Code';
    const template = 'otp';
    const context = { otp };

    await this.mailerService.sendMail({
      to: email,
      subject,
      template,
      context,
    });

    this.logger.log(`OTP sent to ${email}`);
  }

  async sendInterviewInvitation(data: InterviewInvitationData): Promise<void> {
    const subject = `Interview Invitation - ${data.position} at ${data.companyName}`;
    const template = 'interview-invitation';
    const context = data;

    console.log(data, 'this is the data');

    await this.mailerService.sendMail({
      to: data.candidateEmail,
      subject,
      template,
      context,
    });

    this.logger.log(`Interview invitation sent to ${data.candidateEmail}`);
  }
}

import { Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { join } from 'path';
import { escape } from 'lodash'; // Optional if you want to escape user input
import { MailerService } from '@nestjs-modules/mailer';
@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private readonly mailerService: MailerService) {
    this.transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: Number(process.env.EMAIL_PORT),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    this.setupHandlebars();

    // Log connection status on startup with context
    this.transporter.verify((error, success) => {
      const context = `[${process.env.NODE_ENV}] [${process.env.EMAIL_HOST}:${process.env.EMAIL_PORT}]`;
      if (error) {
        this.logger.error(
          `${context} [EmailService] Connection failed: ${error.message}`,
        );
      } else {
        this.logger.log(
          `${context} [EmailService] Connection to email server established successfully.`,
        );
      }
    });
  }

  private async setupHandlebars() {
    try {
      const { default: nodemailerExpressHandlebars } = await import(
        'nodemailer-express-handlebars'
      );

      this.transporter.use(
        'compile',
        nodemailerExpressHandlebars({
          viewEngine: {
            extname: '.hbs',
            partialsDir: join(__dirname, 'templates'),
            defaultLayout: 'otp.hbs',
          },
          viewPath: join(__dirname, 'templates'),
          extName: '.hbs',
        }),
      );
    } catch (error) {
      this.logger.error('Failed to setup handlebars template engine:', error);
    }
  }

  async sendMail(options: any): Promise<void> {
    try {
      const info = await this.transporter.sendMail(options);
      const logMsg = `[EmailService] SUCCESS: Email sent to ${options.to} | Subject: ${options.subject} | MessageId: ${info.messageId}`;
      this.logger.log(logMsg);
      console.log(logMsg);
    } catch (error) {
      const logMsg = `[EmailService] FAILURE: Could not send email to ${options.to} | Subject: ${options.subject} | Error: ${error.message}`;
      this.logger.error(logMsg);
      console.error(logMsg);
      throw error;
    }
  }

  async sendOtpEmail(to: string, otp: string): Promise<void> {
    const subject = 'Your OTP Code';
    const logPrefix = `[EmailService] OTP Email`;
    this.logger.log(
      `${logPrefix} - Attempting to send OTP email to ${to} | Subject: ${subject}`,
    );
    console.log(
      `${logPrefix} - Attempting to send OTP email to ${to} | Subject: ${subject}`,
    );
    try {
      await this.sendMail({
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to,
        subject,
        // template: 'otp',
        context: { otp },
        html: `<p>Your OTP code is: <b>${otp}</b></p>`,
      } as any);
      this.logger.log(`${logPrefix} - OTP email sent successfully to ${to}`);
      console.log(`${logPrefix} - OTP email sent successfully to ${to}`);
    } catch (error) {
      this.logger.error(
        `${logPrefix} - Failed to send OTP email to ${to} | Error: ${error.message}`,
      );
      console.error(
        `${logPrefix} - Failed to send OTP email to ${to} | Error: ${error.message}`,
      );
      throw error;
    }
  }

  async sendInterviewInvitation(interviewData: {
    candidateEmail: string;
    candidateFirstName: string;
    candidateLastName: string;
    interviewDate: string;
    interviewTime: string;
    interviewType: string;
    interviewMode: string;
    location?: string;
    meetingLink?: string;
    interviewerName?: string;
    customMessage?: string;
    position: string;
    companyName: string;
    companyEmail?: string;
    hrName?: string;
  }): Promise<void> {
    const subject = `Interview Invitation - ${interviewData.position} at ${interviewData.companyName}`;
    const logPrefix = `[EmailService] Interview Invitation`;

    const locationLine =
      interviewData.interviewMode === 'Online' && interviewData.meetingLink
        ? `📍 <strong>Location/Link:</strong> ${escape(interviewData.meetingLink)}`
        : interviewData.location
          ? `📍 <strong>Location:</strong> ${escape(interviewData.location)}`
          : '';

    const interviewerLine = interviewData.interviewerName
      ? `🧑‍💼 <strong>Interviewer(s):</strong> ${escape(interviewData.interviewerName)}`
      : '';

    const emailBodyHtml = `
  <div style="border: 2px dashed #a78bfa; padding: 20px; border-radius: 10px; font-family: Arial, sans-serif; background-color: #f9f5ff;">
    <h3>Email Template </h3>
    <p><strong>Subject:</strong> Interview Invitation - ${escape(interviewData.position)} at ${escape(interviewData.companyName)}</p>

    <p>Hi ${escape(interviewData.candidateFirstName)} ${escape(interviewData.candidateLastName)},</p>

    <p>Thank you for applying for the position of <strong>${escape(interviewData.position)}</strong> at <strong>${escape(interviewData.companyName)}</strong>. We would like to invite you for an interview.</p>

    <ul style="list-style: none; padding-left: 0;">
      <li>📅 <strong>Date:</strong> ${escape(interviewData.interviewDate)}</li>
      <li>🕒 <strong>Time:</strong> ${escape(interviewData.interviewTime)}</li>
      ${locationLine ? `<li>${locationLine}</li>` : ''}
      ${interviewerLine ? `<li>${interviewerLine}</li>` : ''}
    </ul>

    ${interviewData.customMessage ? `<p>${escape(interviewData.customMessage)}</p>` : ''}

    <p>Please confirm your availability by replying to this email.</p>

    <p>Best regards,<br>
    ${escape(interviewData.hrName || 'HR Team')}<br>
    ${escape(interviewData.companyName)}</p>
  </div>
  `.trim();

    this.logger.log(
      `${logPrefix} - Attempting to send interview invitation to ${interviewData.candidateEmail} | Subject: ${subject}`,
    );

    try {
      await this.sendMail({
        from:
          interviewData.companyEmail ||
          process.env.EMAIL_FROM ||
          '<EMAIL>',
        to: interviewData.candidateEmail,
        subject,
        html: emailBodyHtml,
      });

      this.logger.log(
        `${logPrefix} - Email sent successfully to ${interviewData.candidateEmail}`,
      );
    } catch (error) {
      this.logger.error(
        `${logPrefix} - Failed to send email: ${error.message}`,
      );
      throw error;
    }
  }

  async sendOfferLetter(offerData: {
    candidateEmail: string;
    candidateFirstName: string;
    candidateLastName: string;
    position: string;
    companyName: string;
    companyEmail: string;
    companyPhone?: string;
    companyWebsite?: string;
    companyAddress?: string;
    startDate: string;
    employmentType: string;
    location: string;
    reportingTo: string;
    currency: string;
    totalMonthlySalary: number;
    basicSalary: number;
    housing: number;
    transportation: number;
    others: number;
    benefits?: Array<{
      allowance: string;
      frequency: string;
      amount: number;
    }>;
    acceptanceDeadline: string;
    hrManagerName: string;
    hrEmail: string;
    hrPhone?: string;
  }): Promise<void> {
    const subject = `Job Offer - ${offerData.position} at ${offerData.companyName}`;
    const logPrefix = `[EmailService] Offer Letter`;

    try {
      this.logger.log(`${logPrefix} - Sending to: ${offerData.candidateEmail}`);

      const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      // Create benefits HTML if benefits exist
      const benefitsHtml =
        offerData.benefits && offerData.benefits.length > 0
          ? `
          <div style="background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;">
            <h3 style="color: #333; margin-top: 0;">🎁 Other Benefits:</h3>
            <ul style="list-style-type: none; padding-left: 0;">
              ${offerData.benefits
                .map(
                  (benefit) =>
                    `<li style="margin: 8px 0; padding: 5px 0;"><strong>${escape(benefit.allowance)}:</strong> ${escape(benefit.frequency)} - ${escape(offerData.currency)} ${benefit.amount}</li>`,
                )
                .join('')}
            </ul>
          </div>
        `
          : '';

      const emailBodyHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Job Offer Letter</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { border: 2px dashed #a78bfa; padding: 30px; border-radius: 0 0 10px 10px; background-color: #f9f5ff; }
                .offer-details { background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea; }
                .compensation { background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981; }
                .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; color: #6b7280; }
                ul { list-style-type: none; padding-left: 0; }
                li { margin: 8px 0; padding: 5px 0; }
                .highlight { background-color: #fef3c7; padding: 2px 6px; border-radius: 4px; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎉 SOFTBUILDERS</h1>
                <p>SOFTWARES THAT MAKES DIFFERENCE</p>
            </div>

            <div class="content">
                <p><strong>Date:</strong> ${escape(currentDate)}</p>

                <p>Dear ${escape(offerData.candidateFirstName)} ${escape(offerData.candidateLastName)},</p>

                <p>We are pleased to offer you the position of <span class="highlight">${escape(offerData.position)}</span> at <strong>${escape(offerData.companyName)}</strong>. We were highly impressed by your portfolio and believe your creativity and skills will be a valuable addition to our team.</p>

                <div class="offer-details">
                    <h3 style="color: #333; margin-top: 0;">📋 Offer Details:</h3>
                    <ul>
                        <li><strong>Position Title:</strong> ${escape(offerData.position)}</li>
                        <li><strong>Start Date:</strong> ${escape(offerData.startDate)}</li>
                        <li><strong>Employment Type:</strong> ${escape(offerData.employmentType)}</li>
                        <li><strong>Location:</strong> ${escape(offerData.location)}</li>
                        <li><strong>Reporting To:</strong> ${escape(offerData.reportingTo)}</li>
                    </ul>
                </div>

                <div class="compensation">
                    <h3 style="color: #333; margin-top: 0;">💰 Compensation & Benefits:</h3>
                    <ul>
                        <li><strong>Total Monthly Salary:</strong> ${escape(offerData.currency)} ${offerData.totalMonthlySalary}</li>
                        <li><strong>Basic Salary:</strong> ${escape(offerData.currency)} ${offerData.basicSalary}</li>
                        <li><strong>Housing:</strong> ${escape(offerData.currency)} ${offerData.housing}</li>
                        <li><strong>Transportation:</strong> ${escape(offerData.currency)} ${offerData.transportation}</li>
                        <li><strong>Others:</strong> ${escape(offerData.currency)} ${offerData.others}</li>
                    </ul>
                </div>

                ${benefitsHtml}

                <p>Please sign and return this letter by <strong>${escape(offerData.acceptanceDeadline)}</strong> to confirm your acceptance of the offer. If you have any questions, feel free to reach out to us at <strong>${escape(offerData.hrEmail)}</strong>.</p>

                <p>We are excited to welcome you to the <strong>${escape(offerData.companyName)}</strong> family and look forward to your creative contributions!</p>

                <p>Warm regards,<br>
                <strong>${escape(offerData.hrManagerName)}</strong><br>
                HR Manager<br>
                ${escape(offerData.hrPhone)} | ${escape(offerData.hrEmail)}</p>

                <div class="footer">
                    <p><strong>${escape(offerData.companyAddress || '')}</strong> | ${escape(offerData.companyPhone || '')} | ${escape(offerData.companyEmail)} | ${escape(offerData.companyWebsite || '')}</p>
                </div>
            </div>
        </body>
        </html>
      `.trim();

      await this.sendMail({
        from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
        to: offerData.candidateEmail,
        subject: subject,
        html: emailBodyHtml,
      });

      this.logger.log(
        `${logPrefix} - Successfully sent to: ${offerData.candidateEmail}`,
      );
    } catch (error) {
      this.logger.error(
        `${logPrefix} - Failed to send email: ${error.message}`,
      );
      throw error;
    }
  }
  async sendOfferLetterUsingHandlebars(offerData: {
    candidateEmail: string;
    candidateFirstName: string;
    candidateLastName: string;
    position: string;
    companyName: string;
    companyEmail: string;
    startDate: string;
    employmentType: string;
    location: string;
    reportingTo: string;
    currency: string;
    totalMonthlySalary: number;
    basicSalary: number;
    housing: number;
    transportation: number;
    others: number;
    benefits?: Array<{
      allowance: string;
      frequency: string;
      amount: number;
    }>;
    acceptanceDeadline: string;
    hrManagerName: string;
    hrEmail: string;
    hrPhone?: string;
  }): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: '<EMAIL>',
        subject: `Job Offer - ${offerData.position} at ${offerData.companyName}`,
        template: 'offer-letter',
        context: offerData,
      });
      this.logger.log(`Offer letter sent to ${offerData.candidateEmail}`);
    } catch (error) {
      this.logger.error(`Failed to send offer letter: ${error.message}`);
      throw error;
    }
  }
}

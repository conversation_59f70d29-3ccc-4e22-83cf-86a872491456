import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import * as path from 'path';
import * as hbs from 'hbs';
import * as fs from 'fs';
import * as puppeteer from 'puppeteer';
import { InterviewInvitationData } from '../users/interfaces/interview-invitation.interface';
import { pathToFileURL } from 'url';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private readonly mailerService: MailerService) {}

  async generatePdfFromTemplate(templateName: string, data: any) {
    const logoPath = path.resolve(__dirname, 'templates', 'assets', 'logo.png');

    if (!fs.existsSync(logoPath)) {
      console.error('❌ Logo file not found at:', logoPath);
      console.error('Current __dirname:', __dirname);
      console.error('Resolved logo path:', logoPath);
    } else {
      console.log('✅ Logo file found at:', logoPath);
    }

    const logoUrl = pathToFileURL(logoPath).href;
    console.log('🔗 Generated logo URL:', logoUrl);

    // Create base64 encoded logo as fallback
    let logoBase64 = '';
    if (fs.existsSync(logoPath)) {
      try {
        const logoBuffer = fs.readFileSync(logoPath);
        logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
        console.log('📸 Base64 logo generated successfully');
      } catch (error) {
        console.error('❌ Error creating base64 logo:', error);
      }
    }

    // Add logo URL to template data (prefer base64 for better compatibility)
    const templateData = {
      ...data,
      logoUrl: logoBase64 || logoUrl, // Use base64 first, fallback to file URL
      logoPath: logoPath, // Alternative for different template needs
      logoBase64: logoBase64, // Direct base64 access if needed
    };

    console.log('📄 Template data logoUrl:', templateData.logoUrl);

    const templatePath = path.join(
      __dirname,
      'templates',
      `${templateName}.hbs`,
    );
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const compiled = hbs.compile(templateHtml);
    const html = compiled(templateData);

    console.log(
      '🎨 Generated HTML contains logo URL:',
      html.includes(templateData.logoUrl),
    );

    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--allow-file-access-from-files',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
      ],
    });

    const page = await browser.newPage();

    // Allow file:// images to be rendered
    await page.setContent(html, {
      waitUntil: 'networkidle0',
      timeout: 30000,
    });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
    });

    await browser.close();

    return pdfBuffer;
  }
  async sendOtpEmail(email: string, otp: string): Promise<void> {
    const subject = 'Your OTP Code';
    const template = 'otp';
    const context = { otp };

    await this.mailerService.sendMail({
      to: email,
      subject,
      template,
      context,
    });

    this.logger.log(`OTP sent to ${email}`);
  }

  async sendInterviewInvitation(data: InterviewInvitationData): Promise<void> {
    const subject = `Interview Invitation - ${data.position} at ${data.companyName}`;
    const template = 'interview-invitation';
    const context = data;

    console.log(data, 'this is the data');

    await this.mailerService.sendMail({
      to: data.candidateEmail,
      subject,
      template,
      context,
    });

    this.logger.log(`Interview invitation sent to ${data.candidateEmail}`);
  }
}

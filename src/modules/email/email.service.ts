import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { join } from 'path';
import * as hbs from 'hbs';
import * as fs from 'fs';
import * as puppeteer from 'puppeteer';
import { InterviewInvitationData } from '../users/interfaces/interview-invitation.interface';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private readonly mailerService: MailerService) {}

  async generatePdfFromTemplate(templateName: string, data: any) {
    const templatePath = join(__dirname, 'templates', `${templateName}.hbs`);
    const templateHtml = fs.readFileSync(templatePath, 'utf8');
    const compiled = hbs.compile(templateHtml);
    const html = compiled(data);

    const browser = await puppeteer.launch();
    const page = await browser.newPage();
    await page.setContent(html, { waitUntil: 'networkidle0' });

    const pdfBuffer = await page.pdf({ format: 'A4', printBackground: true });
    await browser.close();

    return pdfBuffer;
  }

  async sendOtpEmail(email: string, otp: string): Promise<void> {
    const subject = 'Your OTP Code';
    const template = 'otp';
    const context = { otp };

    await this.mailerService.sendMail({
      to: email,
      subject,
      template,
      context,
    });

    this.logger.log(`OTP sent to ${email}`);
  }
}

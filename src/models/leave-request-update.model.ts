import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  PrimaryKey,
  AutoIncrement,
} from 'sequelize-typescript';
import { LeaveRequest } from './leave-request.model';
import { User } from './users-models/user.model';
import { LeaveRequestUpdateStatus } from 'src/modules/leave-requests/interface/leave.request.update.status.interface';
import { LeaveRequestTypes } from 'src/modules/leave-requests/interface/leave.request.types.interface';

@Table({})
export class LeaveRequestUpdate extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({ type: DataType.INTEGER, allowNull: false })
  id: number;

  @ForeignKey(() => LeaveRequest)
  @Column({ type: DataType.INTEGER, allowNull: false })
  leaveRequestId: number;

  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER })
  requestedBy: number;

  @Column({ type: DataType.DATE, allowNull: false })
  startDate: Date;

  @Column({ type: DataType.DATE, allowNull: false })
  endDate: Date;

  @Column({ type: DataType.STRING, allowNull: false })
  reasonOfRequestUpdate: string;

  @Column({
    type: DataType.STRING,
    defaultValue: LeaveRequestUpdateStatus.PENDING,
  })
  status: LeaveRequestUpdateStatus;

  @BelongsTo(() => LeaveRequest)
  leaveRequest: LeaveRequest;

  @BelongsTo(() => User)
  requester: User;
}

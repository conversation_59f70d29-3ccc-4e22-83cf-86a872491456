import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';

import { Department } from '../department.model';

import { User } from './user.model';
import {
  ContractTypeEnum,
  EmploymentTypeEnum,
  WorkingDaysEnum,
  WorkingHoursEnum,
} from '../../modules/users/enums/employment.enums';

@Table({})
export class EmploymentDetails extends Model {
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false, unique: true })
  userId: number;

  @BelongsTo(() => User)
  user: User;

  @ForeignKey(() => Department)
  @Column({ type: DataType.INTEGER, allowNull: true })
  departmentId: number;

  @Column({ type: DataType.STRING })
  position: string;

  @Column({ type: DataType.FLOAT })
  salary: number;

  @Column({ type: DataType.DATE })
  hireDate: Date;

  @Column({ type: DataType.STRING, allowNull: false })
  employeeId: string;

  @Column({ type: DataType.STRING })
  contractDuration: string;

  @Column({
    type: DataType.ENUM(...Object.values(ContractTypeEnum)),
  })
  typeOfContract: ContractTypeEnum;

  @Column({
    type: DataType.ENUM(...Object.values(EmploymentTypeEnum)),
  })
  typeOfEmployment: EmploymentTypeEnum;

  @Column({
    type: DataType.ENUM(...Object.values(WorkingHoursEnum).map(String)),
  })
  workingHours: WorkingHoursEnum;

  @Column({
    type: DataType.ENUM(...Object.values(WorkingDaysEnum).map(String)),
  })
  workingDays: WorkingDaysEnum;

  @Column({ type: DataType.INTEGER, defaultValue: 183 })
  probationPeriod: number;

  @Column({
    type: DataType.DATE,
    defaultValue: () => new Date(Date.now() + 183 * 24 * 60 * 60 * 1000), // 183 days from now
  })
  probationEndDate: Date;

  @Column({ type: DataType.STRING })
  designation: string;

  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: true })
  reportingManagerId: number; // User ID of reporting manager

  @Column({ type: DataType.STRING })
  companyEmail: string;

  @Column({ type: DataType.STRING })
  companyPhoneNumber: string;
}

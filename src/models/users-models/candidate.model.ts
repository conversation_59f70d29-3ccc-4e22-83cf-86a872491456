import {
  Column,
  Model,
  Table,
  HasOne,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { DataTypes } from 'sequelize';
import { Compensation } from './compensation.model';
import { InterviewDetails } from './interview-details.model';
import { Department } from '../department.model';
import { Country } from '../address-models/country-model';
import { Position } from '../position.model';
import { CandidateStatus } from '../../modules/users/enums/invitation-status.enum';
import { CandidateFlowStep } from '../../modules/users/enums/candidate-flow-step.enum';

@Table({})
export class Candidate extends Model<Candidate> {
  @Column({
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataTypes.STRING,
    allowNull: false,
  })
  firstName: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  middleName?: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: false,
  })
  lastName: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  phone?: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  gender?: string;

  @ForeignKey(() => Position)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'position_id',
  })
  positionId?: number;

  @BelongsTo(() => Position)
  position?: Position;

  @ForeignKey(() => Department)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'department_id',
  })
  departmentId?: number;

  @BelongsTo(() => Department)
  department: Department;

  @ForeignKey(() => Country)
  @Column({
    type: DataTypes.INTEGER,
    allowNull: true,
    field: 'country_id',
  })
  countryId?: number;

  @BelongsTo(() => Country)
  country: Country;

  @Column({
    type: DataTypes.INTEGER,
    allowNull: true,
  })
  companyId: number;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  resumeURL?: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  passportNumber?: string;

  @Column({
    type: DataTypes.STRING,
    allowNull: true,
  })
  passportURL?: string;

  @Column({
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  })
  passportExpiryDate?: Date;

  @Column({
    type: DataTypes.ENUM(...Object.values(CandidateStatus)),
    allowNull: false,
    defaultValue: CandidateStatus.INVITATION_PENDING,
  })
  status: CandidateStatus;

  @Column({
    type: DataTypes.ENUM(...Object.values(CandidateFlowStep)),
    allowNull: false,
    defaultValue: CandidateFlowStep.INVITATION,
  })
  flowStep: CandidateFlowStep;

  @Column({
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  })
  createdAt: Date;

  @Column({
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  })
  updatedAt: Date;

  @HasOne(() => Compensation)
  compensation: Compensation;

  @HasOne(() => InterviewDetails)
  interview: InterviewDetails;
}

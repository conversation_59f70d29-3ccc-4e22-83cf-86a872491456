import {
  Column,
  Model,
  Table,
  DataType,
  HasMany,
  HasOne,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Country } from '../address-models/country-model';
import { Department } from '../department.model';
import { Address } from '../address-models/address-model';
import { TimeAttendance } from '../time-attendance.model';
import { LeaveRequest } from '../leave-request.model';
import { Payroll } from '../payroll.model';
import { PerformanceReview } from '../performance-review.model';
import { USER_ACCOUNT_STATUS_ENUM } from 'src/utils/enums';
import { UserDetails } from './user-details.model';
import { ContactDetails } from './contact-details.model';
import { EmploymentDetails } from './employment-details.model';
import { BankInfo } from './bank-info.model';
import { UserRole } from '../roles-and-permissions/user-role.model';
import { Compensation } from './compensation.model';
import { Company } from '../company.model';
import { EmployeeFlowStep } from 'src/modules/users/enums/employee-flow-step.enum';

@Table({
  defaultScope: {
    attributes: {
      exclude: ['password', 'refreshToken'],
    },
  },
  scopes: {
    withPassword: {
      attributes: {
        include: ['password'],
      },
    },
    withRefreshToken: {
      attributes: {
        include: ['refreshToken'],
      },
    },
    withSensitiveData: {
      attributes: {
        include: ['password', 'refreshToken'],
      },
    },
  },
})
export class User extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  username: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  password: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  firstName: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  middleName: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  lastName: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    unique: true,
  })
  refreshToken: string;

  @Column({
    type: DataType.ENUM(...Object.values(USER_ACCOUNT_STATUS_ENUM)),
    defaultValue: USER_ACCOUNT_STATUS_ENUM.ACTIVE,
  })
  status: USER_ACCOUNT_STATUS_ENUM;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: EmployeeFlowStep.PERSONAL_DETAILS,
  })
  flowStep: string;

  @Column({
    type: DataType.STRING,
  })
  profileImage: string;

  @Column({
    type: DataType.STRING,
  })
  redirectTo: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'Asia/Dubai',
  })
  timeZone: string;

  @HasMany(() => UserDetails)
  userDetails: UserDetails[];

  @HasMany(() => UserRole)
  userRoles: UserRole[];

  @HasMany(() => ContactDetails)
  contactDetails: ContactDetails[];

  @HasMany(() => EmploymentDetails)
  employmentDetails: EmploymentDetails[];

  @HasMany(() => BankInfo)
  bankInfos: BankInfo[];

  // Belongs To Relationships

  @HasMany(() => Address)
  addresses: Address[];

  @HasMany(() => TimeAttendance)
  timeAttendances: TimeAttendance[];

  @HasMany(() => LeaveRequest)
  leaveRequests: LeaveRequest[];

  @HasMany(() => Payroll)
  payrolls: Payroll[];

  @HasMany(() => PerformanceReview)
  performanceReviews: PerformanceReview[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  otp: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  otpExpiry: Date;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isEmailVerified: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  googleId: string;

  @ForeignKey(() => Company)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  companyId: number;

  @BelongsTo(() => Company)
  company: Company;

  @Column({
    type: DataType.STRING,
  })
  googleAccessToken: string;

  @Column({
    type: DataType.STRING,
  })
  googleRefreshToken: string;

  @Column({
    type: DataType.DATE,
  })
  googleTokenExpiry: Date;

  @Column({
    type: DataType.STRING,
  })
  emergencyName: string;
  @Column({
    type: DataType.STRING,
  })
  emergencyRelation: string;
  @Column({
    type: DataType.STRING,
  })
  emergencyPhone: string;
  @Column({
    type: DataType.STRING,
  })
  companyEmail: string;
  @Column({
    type: DataType.STRING,
  })
  compnayPhone: string;
}

import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';
import { ATTENDENCE_STATUS } from 'src/modules/time-attendance/interface/time.attendence.interface';
import { Company } from './company.model';

@Table({})
export class TimeAttendance extends Model {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @ForeignKey(() => Company)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  companyId: number;

  @Column({
    type: DataType.DATE,
  })
  clockInTime: Date;

  @Column({
    type: DataType.DATE,
  })
  clockOutTime: Date;

  @Column({
    type: DataType.STRING,
  })
  notes: string;

  @Column({
    type: DataType.DATEONLY,
    allowNull: false,
  })
  date: Date;

  @Column({
    type: DataType.ENUM(...Object.values(ATTENDENCE_STATUS)),
  })
  status: ATTENDENCE_STATUS;

  // Relationships
  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Company)
  company: Company;
}

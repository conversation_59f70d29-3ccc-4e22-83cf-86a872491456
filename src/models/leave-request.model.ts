import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';
import { LeaveRequestStatuses } from 'src/modules/leave-requests/interface/leave.request.statuses.interface';
import { LeaveRequestTypes } from 'src/modules/leave-requests/interface/leave.request.types.interface';
import { Company } from './company.model';

@Table({})
export class LeaveRequest extends Model {
  // Primary Key
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  // Foreign Keys
  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @ForeignKey(() => Company)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  companyId: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
  })
  approvedBy: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
  })
  createdBy: number;

  // Core Data Fields
  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  startDate: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
  })
  endDate: Date;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  type: LeaveRequestTypes;

  @Column({
    type: DataType.STRING,
    defaultValue: LeaveRequestStatuses.PENDING,
  })
  status: LeaveRequestStatuses;

  // Optional/Meta Fields
  @Column({
    type: DataType.STRING,
  })
  reasonOfLeave: string;

  @Column({
    type: DataType.STRING,
  })
  reasonOfRejection: string;

  // Associations
  @BelongsTo(() => User, { foreignKey: 'createdBy' })
  createdByUser: User;

  @BelongsTo(() => User, { foreignKey: 'approvedBy' })
  approver: User;

  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Company)
  company: Company;
}

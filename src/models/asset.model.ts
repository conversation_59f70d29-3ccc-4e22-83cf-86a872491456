import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { User } from './users-models/user.model';
import { Company } from './company.model';

export enum AssetStatus {
  AVAILABLE = 'available',
  IN_USE = 'in-use',
  MAINTENANCE = 'maintenance',
  RETIRED = 'retired',
  LOST = 'lost',
  DAMAGED = 'damaged',
  ACTIVE = 'active',
}

@Table({})
export class Asset extends Model<Asset> {
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  assetTag: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  assetType: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  modelSoftware: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  osVersion: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  licenseSerial: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  purchaseDate: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  expirationDate: Date;

  @Column({
    type: DataType.ENUM(...Object.values(AssetStatus)),
    allowNull: false,
    defaultValue: AssetStatus.AVAILABLE,
  })
  status: AssetStatus;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    field: 'assigned_to',
  })
  assignedTo: number;

  @BelongsTo(() => User, { foreignKey: 'assignedTo' })
  assignedUser: User;

  @ForeignKey(() => Company)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    field: 'company_id',
  })
  companyId: number;

  @BelongsTo(() => Company)
  company: Company;

  // @Column({
  //   type: DataType.DECIMAL(10, 2),
  //   allowNull: true,
  // })
  // purchasePrice: number;

  // @Column({
  //   type: DataType.STRING,
  //   allowNull: true,
  // })
  // vendor: string;

  // @Column({
  //   type: DataType.STRING,
  //   allowNull: true,
  // })
  // warrantyInfo: string;

  // @Column({
  //   type: DataType.DATE,
  //   allowNull: true,
  // })
  // warrantyExpiry: Date;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  // })
  // notes: string;

  // @Column({
  //   type: DataType.STRING,
  //   allowNull: true,
  // })
  // location: string;

  // @Column({
  //   type: DataType.STRING,
  //   allowNull: true,
  // })
  // serialNumber: string;
}

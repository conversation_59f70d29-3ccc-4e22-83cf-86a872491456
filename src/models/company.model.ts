import {
  <PERSON>Increment,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { INDUSTRY_TYPES_ENUM } from 'src/modules/company/enum/industry.enum';
import { Address } from './address-models/address-model';
import { ContactDetails } from './users-models/contact-details.model';
import { User } from './users-models/user.model';

@Table({})
export class Company extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column(DataType.INTEGER)
  id: number;

  @Column({
    type: DataType.STRING,
  })
  logo: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  name: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  portalName: string;

  @Column({
    type: DataType.ENUM(...Object.values(INDUSTRY_TYPES_ENUM)),
  })
  industry: INDUSTRY_TYPES_ENUM;

  @Column({
    type: DataType.STRING,
  })
  website: string;

  @Column({
    type: DataType.STRING,
  })
  email: string;

  @ForeignKey(() => Address)
  @Column(DataType.INTEGER)
  addressId: number;

  @BelongsTo(() => Address)
  address: Address;

  @ForeignKey(() => ContactDetails)
  @Column(DataType.INTEGER)
  contactDetailsId: number;

  @BelongsTo(() => ContactDetails)
  contactDetails: ContactDetails;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  userId: number;

  @BelongsTo(() => User)
  user: User;
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Country } from 'src/models/address-models/country-model';
import { User } from 'src/models/users-models/user.model';
import * as bcrypt from 'bcrypt';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { Company } from 'src/models/company.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { Department } from 'src/models/department.model';
import { RedirectSectionEnum } from 'src/utils/redirect-section.enum';
import { TimeAttendance } from 'src/models/time-attendance.model';
import { ATTENDENCE_STATUS } from 'src/modules/time-attendance/interface/time.attendence.interface';
import { UsersService } from 'src/modules/users/services/users.service';
import {
  ContractTypeEnum,
  EmploymentTypeEnum,
  WorkingDaysEnum,
  WorkingHoursEnum,
} from 'src/modules/users/enums/employment.enums';

const DEFAULT_PASSWORD = 'Admin@123';
const SALT_ROUNDS = 10;

@Injectable()
export class PlatformUserSeeder {
  private readonly logger = new Logger(PlatformUserSeeder.name);

  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(EmploymentDetails)
    private readonly employmentDetailsModel: typeof EmploymentDetails,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(TimeAttendance)
    private readonly timeAttendanceModel: typeof TimeAttendance,
    private readonly usersService: UsersService,
  ) {}

  async seed(): Promise<void> {
    const hashedPassword = await this.hashPassword();
    const roles = await this.getRoles();
    const company = await this.companyModel.findOne({
      where: { name: 'Softbuilders LLC' },
    });

    // Executive Management
    const executiveManagementDepartment = await this.departmentModel.findOne({
      where: { name: 'Executive Management' },
    });

    const hrDepartment = await this.departmentModel.findOne({
      where: { name: 'Human Resources' },
    });

    const PLATFORM_USERS = [
      {
        email: '<EMAIL>',
        username: 'companysuperadmin',
        firstName: 'Company',
        lastName: 'Super Admin',
        departmentId: executiveManagementDepartment.id,
        redirectTo: RedirectSectionEnum.DASHBOARD,
        timeZone: 'Asia/Dubai',
      },
      {
        email: '<EMAIL>',
        username: 'companyadmin',
        firstName: 'Company',
        lastName: 'Admin',
        departmentId: hrDepartment.id,
        redirectTo: RedirectSectionEnum.DASHBOARD,
        timeZone: 'Asia/Dubai',
      },
    ];

    for (const platformUser of PLATFORM_USERS) {
      await this.createPlatformUser(
        platformUser,
        hashedPassword,
        roles,
        company.id,
        platformUser.departmentId,
        platformUser.redirectTo,
        platformUser.timeZone,
      );
    }
  }

  private async hashPassword(): Promise<string> {
    return bcrypt.hash(DEFAULT_PASSWORD, SALT_ROUNDS);
  }

  private async getRoles(): Promise<{ superAdmin: Role; admin: Role }> {
    const company = await this.companyModel.findOne({
      where: { name: 'Softbuilders LLC' },
    });

    const [CompanySuperAdminRole, CompanyAdminRole] = await Promise.all([
      this.roleModel.findOne({
        where: {
          name: 'Company Super Admin',
          scope: ROLE_SCOPE_ENUM.COMPANY,
          companyId: company.id,
        },
      }),
      this.roleModel.findOne({
        where: {
          name: 'Company Admin',
          scope: ROLE_SCOPE_ENUM.COMPANY,
          companyId: company.id,
        },
      }),
    ]);

    if (!CompanySuperAdminRole || !CompanyAdminRole) {
      throw new Error('Required roles not found');
    }

    return { superAdmin: CompanySuperAdminRole, admin: CompanyAdminRole };
  }

  private async createPlatformUser(
    adminUser: Partial<User>,
    hashedPassword: string,
    roles: { superAdmin: Role; admin: Role },
    companyId: number,
    departmentId: number,
    redirectTo: RedirectSectionEnum,
    timeZone: string,
  ): Promise<void> {
    // Check if user already exists
    let user = await this.userModel.findOne({
      where: { email: adminUser.email },
    });

    if (user) {
      // Update existing user
      await user.update({
        ...adminUser,
        password: hashedPassword,
        redirectTo: redirectTo,
        timeZone: timeZone,
      });
      this.logger.log(`Updated existing company user: ${adminUser.email}`);
    } else {
      // Create new user
      user = await this.userModel.create({
        ...adminUser,
        password: hashedPassword,
        redirectTo: redirectTo,
        companyId: companyId, // Set companyId on User model
        timeZone: timeZone,
      });
      this.logger.log(`Created new company user: ${adminUser.email}`);
    }

    const role =
      adminUser.email === '<EMAIL>'
        ? roles.superAdmin
        : roles.admin;

    // Ensure user has the correct role
    await this.userRoleModel.findOrCreate({
      where: { userId: user.id, roleId: role.id },
    });

    // Check if employment details already exist
    const existingEmploymentDetails = await this.employmentDetailsModel.findOne(
      {
        where: { userId: user.id },
      },
    );

    // generate employee id
    const employeeId = await this.usersService.generateEmployeeId(companyId);

    const employmentData = {
      userId: user.id,
      departmentId: departmentId,
      employeeId: employeeId,
      typeOfContract: ContractTypeEnum.PERMANENT,
      typeOfEmployment: EmploymentTypeEnum.FULL_TIME,
      workingHours: WorkingHoursEnum.EIGHT_HOURS,
      workingDays: WorkingDaysEnum.FIVE_DAYS,
      position:
        adminUser.email === '<EMAIL>'
          ? 'Company Super Admin'
          : 'Company Admin',
      designation:
        adminUser.email === '<EMAIL>'
          ? 'Company Super Admin'
          : 'Company Admin',
      hireDate: new Date(),
      salary: 0, // Default salary for seed data
    };

    if (existingEmploymentDetails) {
      // Update existing employment details
      await existingEmploymentDetails.update(employmentData);
      this.logger.log(
        `Updated employment details for user: ${adminUser.email}`,
      );
    } else {
      // Create new employment details
      await this.employmentDetailsModel.create(employmentData);
      this.logger.log(
        `Created employment details for user: ${adminUser.email}`,
      );
    }

    // Seed dummy attendance for the past month
    await this.seedDummyAttendance(user.id);
  }

  private async seedDummyAttendance(userId: number) {
    const now = new Date();
    const start = new Date(now);
    start.setDate(now.getDate() - 29); // 30 days including today
    const end = new Date(now);
    end.setDate(now.getDate() - 1); // Set end to yesterday

    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const day = d.getDay();
      // 0 = Sunday, 6 = Saturday (weekend)
      if (day === 0 || day === 6) continue;

      // Different scenarios with probabilities
      const scenario = Math.random();

      if (scenario < 0.7) {
        // Normal work day (70% probability)
        await this.createNormalWorkDay(userId, d);
      } else if (scenario < 0.8) {
        // Overtime day (10% probability)
        await this.createOvertimeDay(userId, d);
      } else if (scenario < 0.85) {
        // Late arrival day (5% probability)
        await this.createLateArrivalDay(userId, d);
      } else if (scenario < 0.9) {
        // Early departure day (5% probability)
        await this.createEarlyDepartureDay(userId, d);
      } else if (scenario < 0.95) {
        // Half day (5% probability)
        await this.createHalfDay(userId, d);
      } else {
        // Sick day (5% probability)
        await this.createSickDay(userId, d);
      }
    }
  }

  private async createNormalWorkDay(userId: number, date: Date) {
    const sessions = 2 + (Math.random() > 0.7 ? 1 : 0); // 30% chance for 3 sessions
    let sessionStart = new Date(date);
    sessionStart.setHours(8, 30 + Math.floor(Math.random() * 30)); // 8:30-8:59

    for (let s = 0; s < sessions; s++) {
      const durationMins = 90 + Math.floor(Math.random() * 90);
      const clockIn = new Date(sessionStart);
      const clockOut = new Date(sessionStart);
      clockOut.setMinutes(clockOut.getMinutes() + durationMins);

      await this.timeAttendanceModel.create({
        userId,
        companyId: 1,
        date: date,
        clockInTime: clockIn,
        clockOutTime: clockOut,
        status: ATTENDENCE_STATUS.PRESENT,
        notes:
          s === 0
            ? 'Morning session'
            : s === 1
              ? 'Afternoon session'
              : 'Extra session',
      });

      // Next session starts 30-90 mins after previous session ends
      sessionStart = new Date(clockOut);
      sessionStart.setMinutes(
        sessionStart.getMinutes() + 30 + Math.floor(Math.random() * 60),
      );
    }
  }

  private async createOvertimeDay(userId: number, date: Date) {
    // Morning session
    const morningStart = new Date(date);
    morningStart.setHours(8, 30 + Math.floor(Math.random() * 30));
    const morningEnd = new Date(morningStart);
    morningEnd.setHours(12, 30 + Math.floor(Math.random() * 30));

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: date,
      clockInTime: morningStart,
      clockOutTime: morningEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Morning session',
    });

    // Afternoon session
    const afternoonStart = new Date(morningEnd);
    afternoonStart.setMinutes(
      afternoonStart.getMinutes() + 60 + Math.floor(Math.random() * 30),
    );
    const afternoonEnd = new Date(afternoonStart);
    afternoonEnd.setHours(17, 30 + Math.floor(Math.random() * 30));

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: date,
      clockInTime: afternoonStart,
      clockOutTime: afternoonEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Afternoon session',
    });

    // Overtime session (after 6 PM)
    const overtimeStart = new Date(afternoonEnd);
    overtimeStart.setMinutes(
      overtimeStart.getMinutes() + 30 + Math.floor(Math.random() * 30),
    );
    const overtimeEnd = new Date(overtimeStart);
    overtimeEnd.setHours(
      20 + Math.floor(Math.random() * 2),
      Math.floor(Math.random() * 60),
    );

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: date,
      clockInTime: overtimeStart,
      clockOutTime: overtimeEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Overtime session',
    });
  }

  private async createLateArrivalDay(userId: number, date: Date) {
    const sessions = 2;
    let sessionStart = new Date(date);
    sessionStart.setHours(10, Math.floor(Math.random() * 60)); // Late arrival 10-11 AM

    for (let s = 0; s < sessions; s++) {
      const durationMins = 90 + Math.floor(Math.random() * 90);
      const clockIn = new Date(sessionStart);
      const clockOut = new Date(sessionStart);
      clockOut.setMinutes(clockOut.getMinutes() + durationMins);

      await this.timeAttendanceModel.create({
        userId,
        companyId: 1,
        date: date,
        clockInTime: clockIn,
        clockOutTime: clockOut,
        status: ATTENDENCE_STATUS.PRESENT,
        notes: s === 0 ? 'Late morning session' : 'Extended afternoon session',
      });

      sessionStart = new Date(clockOut);
      sessionStart.setMinutes(
        sessionStart.getMinutes() + 30 + Math.floor(Math.random() * 60),
      );
    }
  }

  private async createEarlyDepartureDay(userId: number, date: Date) {
    const morningStart = new Date(date);
    morningStart.setHours(8, 30 + Math.floor(Math.random() * 30));
    const morningEnd = new Date(morningStart);
    morningEnd.setHours(12, 30 + Math.floor(Math.random() * 30));

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: date,
      clockInTime: morningStart,
      clockOutTime: morningEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Morning session',
    });

    const afternoonStart = new Date(morningEnd);
    afternoonStart.setMinutes(
      afternoonStart.getMinutes() + 60 + Math.floor(Math.random() * 30),
    );
    const afternoonEnd = new Date(afternoonStart);
    afternoonEnd.setHours(14, 30 + Math.floor(Math.random() * 30)); // Early departure

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: date,
      clockInTime: afternoonStart,
      clockOutTime: afternoonEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Early departure - personal emergency',
    });
  }

  private async createHalfDay(userId: number, date: Date) {
    const start = new Date(date);
    start.setHours(8, 30 + Math.floor(Math.random() * 30));
    const end = new Date(start);
    end.setHours(12, 30 + Math.floor(Math.random() * 30));

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: date,
      clockInTime: start,
      clockOutTime: end,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Half day - personal leave',
    });
  }

  private async createSickDay(userId: number, date: Date) {
    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: date,
      status: ATTENDENCE_STATUS.ABSENT,
      notes: 'Sick leave',
    });
  }
}

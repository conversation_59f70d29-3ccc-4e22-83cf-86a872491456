import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Country } from 'src/models/address-models/country-model';
import { User } from 'src/models/users-models/user.model';
import * as bcrypt from 'bcrypt';
import { UserRole } from 'src/models/roles-and-permissions/user-role.model';
import { Role } from 'src/models/roles-and-permissions/role.model';
import { ROLE_SCOPE_ENUM } from 'src/utils/enums';
import { Company } from 'src/models/company.model';
import { EmploymentDetails } from 'src/models/users-models/employment-details.model';
import { Department } from 'src/models/department.model';
import { RedirectSectionEnum } from 'src/utils/redirect-section.enum';
import { TimeAttendance } from 'src/models/time-attendance.model';
import { ATTENDENCE_STATUS } from 'src/modules/time-attendance/interface/time.attendence.interface';
import { UsersService } from 'src/modules/users/services/users.service';
import {
  ContractTypeEnum,
  EmploymentTypeEnum,
  WorkingDaysEnum,
  WorkingHoursEnum,
} from 'src/modules/users/enums/employment.enums';
import { DateTime } from 'luxon';
import {
  DUBAI_TZ,
  getStartOfDay,
  getLocalDateString,
  localToUTC,
  getEndOfDay,
  isWeekendLocal,
  isHolidayLocal,
} from 'src/utils/date.utils';

const DEFAULT_PASSWORD = 'Admin@123';
const SALT_ROUNDS = 10;

@Injectable()
export class PlatformUserSeeder {
  private readonly logger = new Logger(PlatformUserSeeder.name);

  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(EmploymentDetails)
    private readonly employmentDetailsModel: typeof EmploymentDetails,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(TimeAttendance)
    private readonly timeAttendanceModel: typeof TimeAttendance,
    private readonly usersService: UsersService,
  ) {}

  async seed(): Promise<void> {
    const hashedPassword = await this.hashPassword();
    const roles = await this.getRoles();
    const company = await this.companyModel.findOne({
      where: { name: 'Softbuilders LLC' },
    });

    // Executive Management
    const executiveManagementDepartment = await this.departmentModel.findOne({
      where: { name: 'Executive Management' },
    });

    const hrDepartment = await this.departmentModel.findOne({
      where: { name: 'Human Resources' },
    });

    const PLATFORM_USERS = [
      {
        email: '<EMAIL>',
        username: 'companysuperadmin',
        firstName: 'Company',
        lastName: 'Super Admin',
        departmentId: executiveManagementDepartment.id,
        redirectTo: RedirectSectionEnum.DASHBOARD,
        timeZone: DUBAI_TZ,
      },
      {
        email: '<EMAIL>',
        username: 'companyadmin',
        firstName: 'Company',
        lastName: 'Admin',
        departmentId: hrDepartment.id,
        redirectTo: RedirectSectionEnum.DASHBOARD,
        timeZone: DUBAI_TZ,
      },
    ];

    for (const platformUser of PLATFORM_USERS) {
      await this.createPlatformUser(
        platformUser,
        hashedPassword,
        roles,
        company.id,
        platformUser.departmentId,
        platformUser.redirectTo,
        platformUser.timeZone,
      );
    }
  }

  private async hashPassword(): Promise<string> {
    return bcrypt.hash(DEFAULT_PASSWORD, SALT_ROUNDS);
  }

  private async getRoles(): Promise<{ superAdmin: Role; admin: Role }> {
    const company = await this.companyModel.findOne({
      where: { name: 'Softbuilders LLC' },
    });

    const [CompanySuperAdminRole, CompanyAdminRole] = await Promise.all([
      this.roleModel.findOne({
        where: {
          name: 'Company Super Admin',
          scope: ROLE_SCOPE_ENUM.COMPANY,
          companyId: company.id,
        },
      }),
      this.roleModel.findOne({
        where: {
          name: 'Company Admin',
          scope: ROLE_SCOPE_ENUM.COMPANY,
          companyId: company.id,
        },
      }),
    ]);

    if (!CompanySuperAdminRole || !CompanyAdminRole) {
      throw new Error('Required roles not found');
    }

    return { superAdmin: CompanySuperAdminRole, admin: CompanyAdminRole };
  }

  private async createPlatformUser(
    adminUser: Partial<User>,
    hashedPassword: string,
    roles: { superAdmin: Role; admin: Role },
    companyId: number,
    departmentId: number,
    redirectTo: RedirectSectionEnum,
    timeZone: string,
  ): Promise<void> {
    // Check if user already exists
    let user = await this.userModel.findOne({
      where: { email: adminUser.email },
    });

    if (user) {
      // Update existing user
      await user.update({
        ...adminUser,
        password: hashedPassword,
        redirectTo: redirectTo,
        timeZone: timeZone,
      });
      this.logger.log(`Updated existing company user: ${adminUser.email}`);
    } else {
      // Create new user
      user = await this.userModel.create({
        ...adminUser,
        password: hashedPassword,
        redirectTo: redirectTo,
        companyId: companyId, // Set companyId on User model
        timeZone: timeZone,
      });
      this.logger.log(`Created new company user: ${adminUser.email}`);
    }

    const role =
      adminUser.email === '<EMAIL>'
        ? roles.superAdmin
        : roles.admin;

    // Ensure user has the correct role
    await this.userRoleModel.findOrCreate({
      where: { userId: user.id, roleId: role.id },
    });

    // Check if employment details already exist
    const existingEmploymentDetails = await this.employmentDetailsModel.findOne(
      {
        where: { userId: user.id },
      },
    );

    // generate employee id
    const employeeId = await this.usersService.generateEmployeeId(companyId);

    const employmentData = {
      userId: user.id,
      departmentId: departmentId,
      employeeId: employeeId,
      typeOfContract: ContractTypeEnum.PERMANENT,
      typeOfEmployment: EmploymentTypeEnum.FULL_TIME,
      workingHours: WorkingHoursEnum.EIGHT_HOURS,
      workingDays: WorkingDaysEnum.FIVE_DAYS,
      position:
        adminUser.email === '<EMAIL>'
          ? 'Company Super Admin'
          : 'Company Admin',
      designation:
        adminUser.email === '<EMAIL>'
          ? 'Company Super Admin'
          : 'Company Admin',
      hireDate: new Date(),
      salary: 0, // Default salary for seed data
    };

    if (existingEmploymentDetails) {
      // Update existing employment details
      await existingEmploymentDetails.update(employmentData);
      this.logger.log(
        `Updated employment details for user: ${adminUser.email}`,
      );
    } else {
      // Create new employment details
      await this.employmentDetailsModel.create(employmentData);
      this.logger.log(
        `Created employment details for user: ${adminUser.email}`,
      );
    }

    // Seed dummy attendance for the past month
    await this.seedDummyAttendance(user.id);
  }

  private async seedDummyAttendance(userId: number) {
    // Get user's timezone
    const user = await this.userModel.findByPk(userId);
    const timeZone = user?.timeZone || DUBAI_TZ;

    // Create dates in user's timezone
    const nowInUserTz = getStartOfDay(new Date(), timeZone);
    const startInUserTz = new Date(nowInUserTz);
    startInUserTz.setDate(nowInUserTz.getDate() - 29); // 30 days including today
    const endInUserTz = new Date(nowInUserTz);
    endInUserTz.setDate(nowInUserTz.getDate() - 1); // Set end to yesterday

    for (
      let d = new Date(startInUserTz);
      d <= endInUserTz;
      d.setDate(d.getDate() + 1)
    ) {
      const localDay = getStartOfDay(d, timeZone);
      const day = localDay.getDay();
      // 0 = Sunday, 6 = Saturday (weekend)
      if (day === 0 || day === 6) continue;
      // Different scenarios with probabilities
      const scenario = Math.random();
      if (scenario < 0.7) {
        await this.createNormalWorkDay(userId, localDay, timeZone);
      } else if (scenario < 0.8) {
        await this.createOvertimeDay(userId, localDay, timeZone);
      } else if (scenario < 0.85) {
        await this.createLateArrivalDay(userId, localDay, timeZone);
      } else if (scenario < 0.9) {
        await this.createEarlyDepartureDay(userId, localDay, timeZone);
      } else if (scenario < 0.95) {
        await this.createHalfDay(userId, localDay, timeZone);
      } else {
        await this.createSickDay(userId, localDay);
      }
    }
  }

  private async createNormalWorkDay(
    userId: number,
    date: Date,
    timeZone: string,
  ) {
    const sessions = 2 + (Math.random() > 0.7 ? 1 : 0);
    const base = getStartOfDay(date, timeZone);
    const sessionStartHour = 8;
    const sessionStartMinute = 30 + Math.floor(Math.random() * 30);
    let sessionStartLocal = localToUTC(
      base.getFullYear(),
      base.getMonth() + 1,
      base.getDate(),
      sessionStartHour,
      sessionStartMinute,
      timeZone,
    );
    for (let s = 0; s < sessions; s++) {
      const durationMins = 150 + Math.floor(Math.random() * 60);
      const clockInUTC = sessionStartLocal;
      const clockOutUTC = new Date(clockInUTC.getTime() + durationMins * 60000);
      await this.timeAttendanceModel.create({
        userId,
        companyId: 1,
        date: getStartOfDay(date, timeZone),
        clockInTime: clockInUTC,
        clockOutTime: clockOutUTC,
        status: ATTENDENCE_STATUS.PRESENT,
        notes:
          s === 0
            ? 'Morning session'
            : s === 1
              ? 'Afternoon session'
              : 'Extra session',
      });
      // Next session starts 1-1.5 hours after previous session
      sessionStartLocal = new Date(
        clockOutUTC.getTime() + (60 + Math.floor(Math.random() * 30)) * 60000,
      );
    }
  }

  private async createOvertimeDay(
    userId: number,
    date: Date,
    timeZone: string,
  ) {
    const base = getStartOfDay(date, timeZone);
    // Morning session: 8:30-9:00 AM start
    const morningStart = localToUTC(
      base.getFullYear(),
      base.getMonth() + 1,
      base.getDate(),
      8,
      30,
      timeZone,
    );
    const morningEnd = new Date(
      morningStart.getTime() + (180 + Math.floor(Math.random() * 30)) * 60000,
    );
    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: getStartOfDay(date, timeZone),
      clockInTime: morningStart,
      clockOutTime: morningEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Morning session',
    });
    // Afternoon session
    const afternoonStart = new Date(
      morningEnd.getTime() + (60 + Math.floor(Math.random() * 30)) * 60000,
    );
    const afternoonEnd = new Date(
      afternoonStart.getTime() + (240 + Math.floor(Math.random() * 30)) * 60000,
    );
    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: getStartOfDay(date, timeZone),
      clockInTime: afternoonStart,
      clockOutTime: afternoonEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Afternoon session',
    });
    // Overtime session
    const overtimeStart = new Date(
      afternoonEnd.getTime() + (30 + Math.floor(Math.random() * 15)) * 60000,
    );
    const overtimeEnd = new Date(
      overtimeStart.getTime() + (120 + Math.floor(Math.random() * 60)) * 60000,
    );
    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: getStartOfDay(date, timeZone),
      clockInTime: overtimeStart,
      clockOutTime: overtimeEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Overtime session',
    });
  }

  private async createLateArrivalDay(
    userId: number,
    date: Date,
    timeZone: string,
  ) {
    // Late start: 10:00-10:30 AM
    let sessionStart = localToUTC(
      date.getFullYear(),
      date.getMonth() + 1,
      date.getDate(),
      10,
      0,
      timeZone,
    );

    for (let s = 0; s < 2; s++) {
      const durationMins = 180 + Math.floor(Math.random() * 60); // 3-4 hours per session
      const clockIn = localToUTC(
        date.getFullYear(),
        date.getMonth() + 1,
        date.getDate(),
        10,
        0,
        timeZone,
      );
      const clockOut = new Date(clockIn.getTime() + durationMins * 60000);

      await this.timeAttendanceModel.create({
        userId,
        companyId: 1,
        date: getStartOfDay(date, timeZone),
        clockInTime: clockIn,
        clockOutTime: clockOut,
        status: ATTENDENCE_STATUS.PRESENT,
        notes: s === 0 ? 'Late morning session' : 'Extended afternoon session',
      });

      // Next session after 1-hour break
      sessionStart = new Date(clockOut.getTime() + 60 * 60000);
    }
  }

  private async createEarlyDepartureDay(
    userId: number,
    date: Date,
    timeZone: string,
  ) {
    // Normal morning start: 8:30-9:00 AM
    const morningStart = localToUTC(
      date.getFullYear(),
      date.getMonth() + 1,
      date.getDate(),
      8,
      30,
      timeZone,
    );
    const morningEnd = new Date(
      morningStart.getTime() + (180 + Math.floor(Math.random() * 30)) * 60000,
    ); // 3-3.5 hours

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: getStartOfDay(date, timeZone),
      clockInTime: morningStart,
      clockOutTime: morningEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Morning session',
    });

    // Short afternoon: 1-hour break, then 2-2.5 hours work
    const afternoonStart = new Date(morningEnd.getTime() + 60 * 60000);
    const afternoonEnd = new Date(
      afternoonStart.getTime() + (120 + Math.floor(Math.random() * 30)) * 60000,
    );

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: getStartOfDay(date, timeZone),
      clockInTime: afternoonStart,
      clockOutTime: afternoonEnd,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Early departure - personal emergency',
    });
  }

  private async createHalfDay(userId: number, date: Date, timeZone: string) {
    // Morning only: 8:30-9:00 AM start, 4-4.5 hours
    const start = localToUTC(
      date.getFullYear(),
      date.getMonth() + 1,
      date.getDate(),
      8,
      30,
      timeZone,
    );
    const end = new Date(
      start.getTime() + (240 + Math.floor(Math.random() * 30)) * 60000,
    );

    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: getStartOfDay(date, timeZone),
      clockInTime: start,
      clockOutTime: end,
      status: ATTENDENCE_STATUS.PRESENT,
      notes: 'Half day - personal leave',
    });
  }

  private async createSickDay(userId: number, date: Date) {
    await this.timeAttendanceModel.create({
      userId,
      companyId: 1,
      date: date,
      status: ATTENDENCE_STATUS.ABSENT,
      notes: 'Sick leave',
    });
  }
}

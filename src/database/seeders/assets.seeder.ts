import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Asset, AssetStatus } from '../../models/asset.model';
import { User } from '../../models/users-models/user.model';
import { Company } from '../../models/company.model';

@Injectable()
export class AssetsSeeder {
  constructor(
    @InjectModel(Asset)
    private assetModel: typeof Asset,
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(Company)
    private companyModel: typeof Company,
  ) {}

  async seed(): Promise<void> {
    try {
      console.log('Starting asset seeding...');

      // Get companies and users for assignment
      const companies = await this.companyModel.findAll();
      if (companies.length === 0) {
        console.log('No companies found. Skipping asset seeding.');
        return;
      }

      const users = await this.userModel.findAll({
        where: { companyId: companies[0].id },
        limit: 5,
      });

      // Sample asset data based on the images
      const assetData = [
        {
          assetTag: 'LT-10234',
          assetType: 'LAPTOP',
          modelSoftware: 'MacBook Pro M2',
          osVersion: 'macOS 14',
          licenseSerial: 'C02YG3AACDE',
          purchaseDate: new Date('2024-01-10'),
          status: AssetStatus.IN_USE,
          assignedTo: users.length > 0 ? users[0].id : null,
          companyId: companies[0].id,
          purchasePrice: 2499.99,
          vendor: 'Apple',
          warrantyInfo: '3 years AppleCare',
          warrantyExpiry: new Date('2027-01-10'),
          location: 'Office Floor 1',
          serialNumber: 'C02YG3AACDE',
        },
        {
          assetTag: 'MN-7002',
          assetType: 'MONITOR',
          modelSoftware: 'UltraSharp 27" 4K',
          licenseSerial: 'CN2344BCDE',
          purchaseDate: new Date('2023-07-15'),
          status: AssetStatus.IN_USE,
          assignedTo: users.length > 1 ? users[1].id : null,
          companyId: companies[0].id,
          purchasePrice: 599.99,
          vendor: 'Dell',
          warrantyInfo: '3 years standard warranty',
          warrantyExpiry: new Date('2026-07-15'),
          location: 'Office Floor 2',
          serialNumber: 'CN2344BCDE',
        },
        {
          assetTag: 'KB-77211',
          assetType: 'KEYBOARD',
          modelSoftware: 'Keychron K4',
          purchaseDate: new Date('2023-05-20'),
          status: AssetStatus.AVAILABLE,
          companyId: companies[0].id,
          purchasePrice: 89.99,
          vendor: 'Keychron',
          warrantyInfo: '1 year warranty',
          warrantyExpiry: new Date('2024-05-20'),
          location: 'Storage Room',
          serialNumber: 'KCH456-6890',
        },
        {
          assetTag: 'MS-55421',
          assetType: 'MOUSE',
          modelSoftware: 'Logitech MX Master 3S',
          purchaseDate: new Date('2023-08-02'),
          status: AssetStatus.IN_USE,
          assignedTo: users.length > 2 ? users[2].id : null,
          companyId: companies[0].id,
          purchasePrice: 99.99,
          vendor: 'Logitech',
          warrantyInfo: '2 years warranty',
          warrantyExpiry: new Date('2025-08-02'),
          location: 'Office Floor 1',
          serialNumber: 'LMX-5568-AA22',
        },
        {
          assetTag: 'SW-00452',
          assetType: 'IDE_LICENSE',
          modelSoftware: 'JetBrains All Products',
          osVersion: 'v2024.1',
          licenseSerial: 'JB-LIC-2024-XYZ',
          purchaseDate: new Date('2024-02-01'),
          expirationDate: new Date('2025-01-31'),
          status: AssetStatus.ACTIVE,
          assignedTo: users.length > 3 ? users[3].id : null,
          companyId: companies[0].id,
          purchasePrice: 649.0,
          vendor: 'JetBrains',
          notes: 'Annual subscription for development team',
        },
        {
          assetTag: 'SW-00577',
          assetType: 'DESIGN_TOOL',
          modelSoftware: 'Figma Organization Plan',
          osVersion: 'v2025-01',
          licenseSerial: 'FIGMA-ORG-555',
          purchaseDate: new Date('2025-01-01'),
          expirationDate: new Date('2026-01-01'),
          status: AssetStatus.ACTIVE,
          companyId: companies[0].id,
          purchasePrice: 1440.0,
          vendor: 'Figma',
          notes: 'Design team collaboration tool',
        },
        {
          assetTag: 'LT-20078',
          assetType: 'LAPTOP',
          modelSoftware: 'Dell XPS 15',
          osVersion: 'Windows 11 Pro',
          licenseSerial: 'DELL-XPS-9910-SN',
          purchaseDate: new Date('2023-10-10'),
          status: AssetStatus.IN_USE,
          assignedTo: users.length > 4 ? users[4].id : null,
          companyId: companies[0].id,
          purchasePrice: 1899.99,
          vendor: 'Dell',
          warrantyInfo: '3 years ProSupport',
          warrantyExpiry: new Date('2026-10-10'),
          location: 'Office Floor 2',
          serialNumber: 'DELL-XPS-9910-SN',
        },
        {
          assetTag: 'HD-90871',
          assetType: 'EXTERNAL_HDD',
          modelSoftware: 'WD Elements 2TB',
          licenseSerial: 'WD-ELM-21-7789',
          purchaseDate: new Date('2022-09-01'),
          status: AssetStatus.AVAILABLE,
          companyId: companies[0].id,
          purchasePrice: 79.99,
          vendor: 'Western Digital',
          warrantyInfo: '2 years limited warranty',
          warrantyExpiry: new Date('2024-09-01'),
          location: 'IT Storage',
          serialNumber: 'WD-ELM-21-7789',
        },
      ];

      // Create assets
      for (const asset of assetData) {
        await this.assetModel.findOrCreate({
          where: {
            assetTag: asset.assetTag,
            companyId: asset.companyId,
          },
          defaults: asset,
        });
      }

      console.log('Asset seeding completed successfully');
    } catch (error) {
      console.error('Error seeding assets:', error);
      throw new Error(error);
    }
  }
}

import { ExecutionContext, createParamDecorator } from '@nestjs/common';
import { RequestUserObjectI } from 'src/modules/auth/auth.interfaces';

export const GetUser = createParamDecorator(
  (field: keyof RequestUserObjectI | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user: RequestUserObjectI | undefined = request['user'];
    return field ? user?.[field] : user;
  },
);

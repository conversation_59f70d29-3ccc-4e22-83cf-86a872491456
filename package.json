{"name": "api-hr-master", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed:db": "ts-node -r tsconfig-paths/register ./src/database/seeders/seeds.ts"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.0", "@nestjs/core": "^11.0.0", "@nestjs/event-emitter": "^3.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.0", "@nestjs/platform-express": "^11.0.0", "@nestjs/platform-socket.io": "^11.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/sequelize": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.0.0", "@sendgrid/mail": "^8.1.5", "@types/cookie-parser": "^1.4.9", "@types/passport-google-oauth20": "^2.0.16", "axios": "^1.10.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.7", "handlebars": "^4.7.8", "hbs": "^4.2.0", "ioredis": "^5.6.1", "multer": "^2.0.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.4", "nodemailer-express-handlebars": "^7.0.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "puppeteer": "^24.14.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/config": "^4.0.2", "@nestjs/mapped-types": "^2.1.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/handlebars": "^4.0.40", "@types/ioredis": "^4.28.10", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/sequelize": "^4.28.20", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "prettier": "^3.0.0", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}